using API.Attributes;
using Applications.DTOs.Partners;
using Applications.Features.Partners.Commands;
using Applications.Interfaces.Services.Authorization;
using Infrastructure.Services.Security;
using Mapster;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Shared.Constants;
using Shared.Responses;
using System.Security.Claims;

namespace API.Controllers;

/// <summary>
/// Controller quản trị hệ thống authentication/authorization
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AdminController : ControllerBase
{
    private readonly IPermissionService _permissionService;
    private readonly IRoleService _roleService;
    private readonly IConstraintService _constraintService;
    private readonly IUsageTrackingService _usageTrackingService;
    private readonly IMediator _mediator;
    private readonly SecretMigrationService _secretMigrationService;
    private readonly ILogger<AdminController> _logger;

    public AdminController(
        IPermissionService permissionService,
        IRoleService roleService,
        IConstraintService constraintService,
        IUsageTrackingService usageTrackingService,
        IMediator mediator,
        SecretMigrationService secretMigrationService,
        ILogger<AdminController> logger)
    {
        _permissionService = permissionService;
        _roleService = roleService;
        _constraintService = constraintService;
        _usageTrackingService = usageTrackingService;
        _mediator = mediator;
        _secretMigrationService = secretMigrationService;
        _logger = logger;
    }

    #region Partner Management

    /// <summary>
    /// Tạo partner mới
    /// </summary>
    /// <param name="request">Thông tin partner cần tạo</param>
    /// <returns>Thông tin partner đã tạo</returns>
    [HttpPost("partners")]
    [RequirePermission("ADMIN_MANAGE", "CREATE")]
    public async Task<IActionResult> CreatePartner([FromBody] CreatePartnerRequest request)
    {
        try
        {
            _logger.LogInformation("Creating new partner with ClientId: {ClientId}", request.ClientId);

            // Get current user ID from JWT token
            var currentUserId = GetCurrentUserId();

            // Map request to command
            var command = request.Adapt<CreatePartnerCommand>();
            command.CreatedBy = currentUserId;

            // Execute command
            var result = await _mediator.Send(command);

            if (!result.IsSuccess)
            {
                _logger.LogWarning("Failed to create partner {ClientId}: {Error}", request.ClientId, result.Message);
                return BadRequest(result);
            }

            _logger.LogInformation("Partner created successfully with ClientId: {ClientId}", request.ClientId);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating partner with ClientId: {ClientId}", request.ClientId);
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error while creating partner",
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    /// <summary>
    /// Lấy thông tin quyền hạn của đối tác
    /// </summary>
    /// <param name="partnerId">ID của đối tác</param>
    /// <returns>Thông tin quyền hạn</returns>
    [HttpGet("partners/{partnerId}/permissions")]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> GetPartnerPermissions(Guid partnerId)
    {
        try
        {
            _logger.LogInformation("Getting permissions for partner {PartnerId}", partnerId);

            var result = await _permissionService.GetPartnerPermissionsAsync(partnerId);

            if (!result.IsSuccess)
            {
                return BadRequest(Response<object>.Failure<object>(result.Message, ErrorCodes.BAD_REQUEST_ERROR));
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting partner permissions for {PartnerId}", partnerId);
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error",
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    /// <summary>
    /// Cấp quyền cho đối tác
    /// </summary>
    /// <param name="partnerId">ID của đối tác</param>
    /// <param name="request">Thông tin quyền cần cấp</param>
    /// <returns>Kết quả cấp quyền</returns>
    [HttpPost("partners/{partnerId}/permissions")]
    [RequirePermission("ADMIN_MANAGE", "CREATE")]
    public async Task<IActionResult> GrantPartnerPermission(Guid partnerId, [FromBody] GrantPermissionRequest request)
    {
        try
        {
            _logger.LogInformation("Granting permission {FunctionCode}:{PermissionCode} to partner {PartnerId}",
                request.FunctionCode, request.PermissionCode, partnerId);

            var currentUserId = GetCurrentUserId(); // Implement this method to get current user from JWT

            var result = await _permissionService.GrantPermissionAsync(
                partnerId,
                request.FunctionCode,
                request.PermissionCode,
                currentUserId,
                request.Reason,
                request.ExpiresAt);

            if (!result.IsSuccess)
            {
                return BadRequest(Response<object>.Failure<object>(result.Message, ErrorCodes.BAD_REQUEST_ERROR));
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error granting permission to partner {PartnerId}", partnerId);
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error",
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    /// <summary>
    /// Thu hồi quyền của đối tác
    /// </summary>
    /// <param name="partnerId">ID của đối tác</param>
    /// <param name="request">Thông tin quyền cần thu hồi</param>
    /// <returns>Kết quả thu hồi quyền</returns>
    [HttpDelete("partners/{partnerId}/permissions")]
    [RequirePermission("ADMIN_MANAGE", "DELETE")]
    public async Task<IActionResult> RevokePartnerPermission(Guid partnerId, [FromBody] RevokePermissionRequest request)
    {
        try
        {
            _logger.LogInformation("Revoking permission {FunctionCode}:{PermissionCode} from partner {PartnerId}",
                request.FunctionCode, request.PermissionCode, partnerId);

            var currentUserId = GetCurrentUserId();

            var result = await _permissionService.RevokePermissionAsync(
                partnerId,
                request.FunctionCode,
                request.PermissionCode,
                currentUserId,
                request.Reason);

            if (!result.IsSuccess)
            {
                return BadRequest(Response<object>.Failure<object>(result.Message, ErrorCodes.BAD_REQUEST_ERROR));
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking permission from partner {PartnerId}", partnerId);
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error",
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    #endregion

    #region Role Management

    /// <summary>
    /// Lấy danh sách tất cả vai trò
    /// </summary>
    /// <returns>Danh sách vai trò</returns>
    [HttpGet("roles")]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> GetAllRoles()
    {
        try
        {
            _logger.LogInformation("Getting all roles");

            var result = await _roleService.GetAllRolesAsync();

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all roles");
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error",
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    /// <summary>
    /// Gán vai trò cho đối tác
    /// </summary>
    /// <param name="partnerId">ID của đối tác</param>
    /// <param name="request">Thông tin vai trò cần gán</param>
    /// <returns>Kết quả gán vai trò</returns>
    [HttpPost("partners/{partnerId}/roles")]
    [RequirePermission("ADMIN_MANAGE", "CREATE")]
    public async Task<IActionResult> AssignRoleToPartner(Guid partnerId, [FromBody] AssignRoleRequest request)
    {
        try
        {
            _logger.LogInformation("Assigning role {RoleCode} to partner {PartnerId}", request.RoleCode, partnerId);

            var currentUserId = GetCurrentUserId();

            var result = await _roleService.AssignRoleAsync(
                partnerId,
                request.RoleCode,
                currentUserId,
                request.Reason,
                request.ExpiresAt);

            if (!result.IsSuccess)
            {
                return BadRequest(Response<object>.Failure<object>(result.Message, ErrorCodes.BAD_REQUEST_ERROR));
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role to partner {PartnerId}", partnerId);
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error",
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    #endregion

    #region Constraint Management

    /// <summary>
    /// Lấy thông tin ràng buộc của đối tác
    /// </summary>
    /// <param name="partnerId">ID của đối tác</param>
    /// <returns>Thông tin ràng buộc</returns>
    [HttpGet("partners/{partnerId}/constraints")]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> GetPartnerConstraints(Guid partnerId)
    {
        try
        {
            _logger.LogInformation("Getting constraints for partner {PartnerId}", partnerId);

            var result = await _constraintService.GetPartnerConstraintsAsync(partnerId);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting partner constraints for {PartnerId}", partnerId);
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error",
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    /// <summary>
    /// Thiết lập ràng buộc cho đối tác
    /// </summary>
    /// <param name="partnerId">ID của đối tác</param>
    /// <param name="request">Thông tin ràng buộc</param>
    /// <returns>Kết quả thiết lập</returns>
    [HttpPost("partners/{partnerId}/constraints")]
    [RequirePermission("ADMIN_MANAGE", "CREATE")]
    public async Task<IActionResult> SetPartnerConstraint(Guid partnerId, [FromBody] SetConstraintRequest request)
    {
        try
        {
            _logger.LogInformation("Setting constraint {ConstraintType} for partner {PartnerId}",
                request.ConstraintType, partnerId);

            var currentUserId = GetCurrentUserId();

            var result = await _constraintService.SetConstraintAsync(
                partnerId,
                request.ConstraintType,
                request.ConstraintValue,
                request.ValidFrom,
                request.ValidTo,
                currentUserId,
                request.Description,
                request.Priority);

            if (!result.IsSuccess)
            {
                return BadRequest(Response<object>.Failure<object>(result.Message, ErrorCodes.BAD_REQUEST_ERROR));
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting constraint for partner {PartnerId}", partnerId);
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error",
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    #endregion

    #region Usage Tracking

    /// <summary>
    /// Lấy thống kê sử dụng của đối tác
    /// </summary>
    /// <param name="partnerId">ID của đối tác</param>
    /// <param name="period">Kỳ thống kê (ví dụ: "2024-01")</param>
    /// <returns>Thống kê sử dụng</returns>
    [HttpGet("partners/{partnerId}/usage")]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> GetPartnerUsage(Guid partnerId, [FromQuery] string period)
    {
        try
        {
            _logger.LogInformation("Getting usage statistics for partner {PartnerId}, period {Period}",
                partnerId, period);

            var result = await _usageTrackingService.GetUsageStatisticsAsync(partnerId, period);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage statistics for partner {PartnerId}", partnerId);
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error",
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    #endregion

    #region Security Management

    /// <summary>
    /// Migrate existing plaintext HMAC secrets to encrypted format
    /// </summary>
    /// <returns>Migration result</returns>
    [HttpPost("migrate-hmac-secrets")]
    [RequirePermission("ADMIN_MANAGE", "EXECUTE")]
    public async Task<IActionResult> MigrateHmacSecrets()
    {
        try
        {
            _logger.LogInformation("Starting HMAC secret migration");

            var migratedCount = await _secretMigrationService.MigrateHmacSecretsAsync();

            _logger.LogInformation("HMAC secret migration completed. Migrated {Count} secrets", migratedCount);

            return Ok(Response<object>.Success(new { MigratedCount = migratedCount },
                $"Successfully migrated {migratedCount} HMAC secrets"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during HMAC secret migration");
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error during migration",
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    /// <summary>
    /// Verify that all encrypted HMAC secrets can be decrypted
    /// </summary>
    /// <returns>Verification result</returns>
    [HttpPost("verify-hmac-secrets")]
    [RequirePermission("ADMIN_MANAGE", "READ")]
    public async Task<IActionResult> VerifyHmacSecrets()
    {
        try
        {
            _logger.LogInformation("Starting HMAC secret verification");

            var isValid = await _secretMigrationService.VerifyEncryptedSecretsAsync();

            _logger.LogInformation("HMAC secret verification completed. Result: {IsValid}", isValid);

            return Ok(Response<object>.Success(new { IsValid = isValid },
                isValid ? "All HMAC secrets verified successfully" : "Some HMAC secrets failed verification"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during HMAC secret verification");
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error during verification",
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Get current user ID from JWT token
    /// </summary>
    /// <returns>Current user ID</returns>
    private Guid GetCurrentUserId()
    {
        // For now, return a default admin user ID
        // TODO: Implement proper JWT token parsing
        return Guid.Parse("*************-5555-5555-************"); // Admin user from seed data
    }

    #endregion
}

#region DTOs

/// <summary>
/// Request để cấp quyền
/// </summary>
public class GrantPermissionRequest
{
    public string FunctionCode { get; set; } = null!;
    public string PermissionCode { get; set; } = null!;
    public string Reason { get; set; } = null!;
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// Request để thu hồi quyền
/// </summary>
public class RevokePermissionRequest
{
    public string FunctionCode { get; set; } = null!;
    public string PermissionCode { get; set; } = null!;
    public string Reason { get; set; } = null!;
}

/// <summary>
/// Request để gán vai trò
/// </summary>
public class AssignRoleRequest
{
    public string RoleCode { get; set; } = null!;
    public string Reason { get; set; } = null!;
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// Request để thiết lập ràng buộc
/// </summary>
public class SetConstraintRequest
{
    public string ConstraintType { get; set; } = null!;
    public string ConstraintValue { get; set; } = null!;
    public DateTime ValidFrom { get; set; } = DateTime.UtcNow;
    public DateTime? ValidTo { get; set; }
    public string? Description { get; set; }
    public int Priority { get; set; } = 100;
}

#endregion