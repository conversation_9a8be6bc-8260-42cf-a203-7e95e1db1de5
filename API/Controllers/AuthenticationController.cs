using API.Attributes;
using Applications.Interfaces.Services.Authentication;
using Microsoft.AspNetCore.Mvc;
using Shared.Constants;
using Shared.Responses;

namespace API.Controllers;

/// <summary>
/// Controller xử lý xác thực OAuth2 Client Credentials
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AuthenticationController(IAuthenticationService authenticationService,
    ITokenService tokenService,
    ISignatureService signatureService,
    IIpWhitelistService ipWhitelistService,
    ILogger<AuthenticationController> logger)
    : ControllerBase
{

    /// <summary>
    /// OAuth2 Client Credentials Token Endpoint
    /// </summary>
    /// <param name="request">Token request</param>
    /// <returns>Access token response</returns>
    [HttpPost("token")]
    [AllowAnonymous]
    public async Task<IActionResult> GetToken([FromBody] TokenRequest request)
    {
        try
        {
            // Validate request
            if (string.IsNullOrWhiteSpace(request.ClientId) || 
                string.IsNullOrWhiteSpace(request.ClientSecret) ||
                request.GrantType != "client_credentials")
            {
                logger.LogWarning("Invalid token request from {IpAddress}", GetClientIpAddress());
                return BadRequest(Response<object>.Failure<object>(
                    "Invalid request parameters", 
                    ErrorCodes.BAD_REQUEST_ERROR));
            }

            // Get client IP
            var clientIp = GetClientIpAddress();
            var userAgent = Request.Headers.UserAgent.ToString();

            logger.LogInformation("Token request from client {ClientId}, IP {IpAddress}", 
                request.ClientId, clientIp);

            // Authenticate
            var authResult = await authenticationService.AuthenticateAsync(
                request.ClientId, 
                request.ClientSecret, 
                clientIp, 
                userAgent);

            if (!authResult.IsSuccess || authResult.Data == null)
            {
                logger.LogWarning("Authentication failed for client {ClientId}, IP {IpAddress}: {Error}", 
                    request.ClientId, clientIp, authResult.Message);
                
                return Unauthorized(Response<object>.Failure<object>(
                    "Authentication failed", 
                    ErrorCodes.UNAUTHORIZED_ERROR));
            }

            // Return token response
            var tokenResponse = new TokenResponse
            {
                AccessToken = authResult.Data.AccessToken,
                TokenType = "Bearer",
                ExpiresIn = authResult.Data.ExpiresIn,
                Scope = string.Join(" ", authResult.Data.Scopes ?? Array.Empty<string>())
            };

            logger.LogInformation("Token issued successfully for client {ClientId}", request.ClientId);

            return Ok(Response<TokenResponse>.Success(tokenResponse, "Token issued successfully"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing token request");
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error", 
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    /// <summary>
    /// Validate access token
    /// </summary>
    /// <param name="token">Access token to validate</param>
    /// <returns>Token validation result</returns>
    [HttpPost("validate")]
    [AllowAnonymous]
    public async Task<IActionResult> ValidateToken([FromBody] ValidateTokenRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.AccessToken))
            {
                return BadRequest(Response<object>.Failure<object>(
                    "Access token is required", 
                    ErrorCodes.BAD_REQUEST_ERROR));
            }

            logger.LogInformation("Token validation request from {IpAddress}", GetClientIpAddress());

            var validationResult = await authenticationService.ValidateTokenAsync(request.AccessToken);

            if (!validationResult.IsSuccess || validationResult.Data == null)
            {
                logger.LogWarning("Token validation failed: {Error}", validationResult.Message);
                return Unauthorized(Response<object>.Failure<object>(
                    "Invalid token", 
                    ErrorCodes.UNAUTHORIZED_ERROR));
            }

            return Ok(Response<TokenValidationResult>.Success(
                validationResult.Data, 
                "Token is valid"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error validating token");
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error", 
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    /// <summary>
    /// Revoke access token
    /// </summary>
    /// <param name="request">Revoke token request</param>
    /// <returns>Revocation result</returns>
    [HttpPost("revoke")]
    [AllowAnonymous]
    public async Task<IActionResult> RevokeToken([FromBody] RevokeTokenRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.AccessToken))
            {
                return BadRequest(Response<object>.Failure<object>(
                    "Access token is required", 
                    ErrorCodes.BAD_REQUEST_ERROR));
            }

            logger.LogInformation("Token revocation request from {IpAddress}", GetClientIpAddress());

            var revokeResult = await authenticationService.RevokeTokenAsync(
                request.AccessToken, 
                request.Reason ?? "Requested by client");

            if (!revokeResult.IsSuccess)
            {
                logger.LogWarning("Token revocation failed: {Error}", revokeResult.Message);
                return BadRequest(Response<object>.Failure<object>(
                    revokeResult.Message, 
                    ErrorCodes.BAD_REQUEST_ERROR));
            }

            logger.LogInformation("Token revoked successfully");
            return Ok(Response<bool>.Success(true, "Token revoked successfully"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error revoking token");
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error", 
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    /// <summary>
    /// Get current timestamp for signature generation
    /// </summary>
    /// <returns>Current Unix timestamp</returns>
    [HttpGet("timestamp")]
    public IActionResult GetTimestamp()
    {
        try
        {
            var timestamp = signatureService.GetCurrentTimestamp();
            
            return Ok(Response<TimestampResponse>.Success(
                new TimestampResponse
                {
                    Timestamp = timestamp,
                    ServerTime = DateTime.UtcNow,
                    ValiditySeconds = 300 // 5 minutes validity
                }, 
                "Current timestamp"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting timestamp");
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error", 
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    /// <summary>
    /// Check if client IP is whitelisted
    /// </summary>
    /// <returns>IP whitelist check result</returns>
    [HttpGet("ip-check")]
    public async Task<IActionResult> CheckIpWhitelist()
    {
        try
        {
            var clientIp = GetClientIpAddress();
            
            logger.LogInformation("IP whitelist check request from {IpAddress}", clientIp);

            // For demo purposes, we'll just return the IP and validation status
            var isValid = ipWhitelistService.IsValidIpAddress(clientIp);
            
            return Ok(Response<IpCheckResponse>.Success(
                new IpCheckResponse
                {
                    IpAddress = clientIp,
                    IsValidFormat = isValid,
                    Message = isValid ? "IP address format is valid" : "Invalid IP address format"
                }, 
                "IP check completed"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error checking IP whitelist");
            return StatusCode(500, Response<object>.Failure<object>(
                "Internal server error", 
                ErrorCodes.INTERNAL_SERVER_ERROR));
        }
    }

    /// <summary>
    /// Get client IP address considering proxy headers
    /// </summary>
    /// <returns>Client IP address</returns>
    private string GetClientIpAddress()
    {
        return ipWhitelistService.GetClientIpAddress(HttpContext);
    }
}

#region DTOs

/// <summary>
/// OAuth2 Client Credentials token request
/// </summary>
public class TokenRequest
{
    public string ClientId { get; set; } = null!;
    public string ClientSecret { get; set; } = null!;
    public string GrantType { get; set; } = "client_credentials";
    public string? Scope { get; set; }
}

/// <summary>
/// OAuth2 token response
/// </summary>
public class TokenResponse
{
    public string AccessToken { get; set; } = null!;
    public string TokenType { get; set; } = "Bearer";
    public int ExpiresIn { get; set; }
    public string? Scope { get; set; }
}

/// <summary>
/// Token validation request
/// </summary>
public class ValidateTokenRequest
{
    public string AccessToken { get; set; } = null!;
}

/// <summary>
/// Token revocation request
/// </summary>
public class RevokeTokenRequest
{
    public string AccessToken { get; set; } = null!;
    public string? Reason { get; set; }
}

/// <summary>
/// Timestamp response
/// </summary>
public class TimestampResponse
{
    public string Timestamp { get; set; } = null!;
    public DateTime ServerTime { get; set; }
    public int ValiditySeconds { get; set; }
}

/// <summary>
/// IP check response
/// </summary>
public class IpCheckResponse
{
    public string IpAddress { get; set; } = null!;
    public bool IsValidFormat { get; set; }
    public string Message { get; set; } = null!;
}

#endregion