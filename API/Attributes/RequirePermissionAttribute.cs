namespace API.Attributes;

/// <summary>
/// Attribute để chỉ định quyền hạn cần thiết cho một action
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = true)]
public class RequirePermissionAttribute : Attribute
{
    /// <summary>
    /// Mã chức năng cần kiểm tra quyền
    /// </summary>
    public string FunctionCode { get; }

    /// <summary>
    /// Mã quyền hạn cần thiết
    /// </summary>
    public string PermissionCode { get; }

    /// <summary>
    /// Khởi tạo attribute với function code và permission code
    /// </summary>
    /// <param name="functionCode">Mã chức năng (ví dụ: "INVOICE_CREATE")</param>
    /// <param name="permissionCode">Mã quyền hạn (ví dụ: "CREATE")</param>
    public RequirePermissionAttribute(string functionCode, string permissionCode)
    {
        FunctionCode = functionCode ?? throw new ArgumentNullException(nameof(functionCode));
        PermissionCode = permissionCode ?? throw new ArgumentNullException(nameof(permissionCode));
    }
}

/// <summary>
/// Attribute để chỉ định vai trò cần thiết cho một action
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = true)]
public class RequireRoleAttribute : Attribute
{
    /// <summary>
    /// Mã vai trò cần thiết
    /// </summary>
    public string RoleCode { get; }

    /// <summary>
    /// Khởi tạo attribute với role code
    /// </summary>
    /// <param name="roleCode">Mã vai trò (ví dụ: "ADMIN")</param>
    public RequireRoleAttribute(string roleCode)
    {
        RoleCode = roleCode ?? throw new ArgumentNullException(nameof(roleCode));
    }
}

/// <summary>
/// Attribute để bỏ qua kiểm tra authorization cho một action
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class AllowAnonymousAttribute : Attribute
{
}

/// <summary>
/// Attribute để chỉ định constraint cần kiểm tra
/// </summary>
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = true)]
public class RequireConstraintAttribute : Attribute
{
    /// <summary>
    /// Loại constraint cần kiểm tra
    /// </summary>
    public string ConstraintType { get; }

    /// <summary>
    /// Giá trị tối đa cho phép
    /// </summary>
    public object? MaxValue { get; set; }

    /// <summary>
    /// Khởi tạo attribute với constraint type
    /// </summary>
    /// <param name="constraintType">Loại constraint (ví dụ: "API_RATE_LIMIT_PER_HOUR")</param>
    public RequireConstraintAttribute(string constraintType)
    {
        ConstraintType = constraintType ?? throw new ArgumentNullException(nameof(constraintType));
    }
}