using Applications.Interfaces.Services.Authentication;
using Microsoft.Extensions.Options;
using Shared.Constants;
using Shared.Responses;
using System.Text.Json;

namespace API.Middlewares;

/// <summary>
/// Middleware for IP whitelist validation
/// </summary>
public class IpWhitelistMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<IpWhitelistMiddleware> _logger;
    private readonly AuthenticationOptions _options;

    public IpWhitelistMiddleware(
        RequestDelegate next,
        ILogger<IpWhitelistMiddleware> logger,
        IOptions<AuthenticationOptions> options)
    {
        _next = next;
        _logger = logger;
        _options = options.Value;
    }

    public async Task InvokeAsync(HttpContext context, IIpWhitelistService ipWhitelistService)
    {
        // Skip IP validation for non-protected endpoints
        if (ShouldSkipIpValidation(context))
        {
            await _next(context);
            return;
        }

        try
        {
            var clientIp = ipWhitelistService.GetClientIpAddress(context);
            
            // For now, we'll check IP whitelist in the authentication phase
            // This middleware just logs the IP and prepares it for later validation
            context.Items["ClientIP"] = clientIp;
            
            _logger.LogInformation("IP validation prepared for {IP} on {Path}", 
                clientIp, context.Request.Path);

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in IP whitelist middleware");
            
            var errorResponse = new Response<object>
            {
                Code = ErrorCodes.INTERNAL_SERVER_ERROR,
                Message = "Internal server error during IP validation",
                TraceId = context.TraceIdentifier
            };

            context.Response.StatusCode = 500;
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse));
        }
    }

    private bool ShouldSkipIpValidation(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLower();
        
        if (string.IsNullOrEmpty(path))
            return false;
            
        // Check against configured skip paths
        if (_options.SkipAuthenticationPaths != null)
        {
            foreach (var skipPath in _options.SkipAuthenticationPaths)
            {
                if (path.Contains(skipPath.ToLower()))
                    return true;
            }
        }
        
        // Additional hardcoded paths for basic functionality
        return path.Contains("/swagger") ||
               path.Contains("/health") ||
               path.Contains("/metrics") ||
               path == "/" ||
               path == "/favicon.ico";
    }
}

/// <summary>
/// Configuration options for authentication middleware
/// </summary>
public class AuthenticationOptions
{
    public int TokenExpirationMinutes { get; set; } = 120;
    public int SignatureToleranceSeconds { get; set; } = 300;
    public bool EnableIpWhitelist { get; set; } = true;
    public string DefaultHashAlgorithm { get; set; } = "HMAC-SHA256";
    public bool EnableUsageTracking { get; set; } = true;
    public bool EnableTimestampValidation { get; set; } = true;
    public string[] SkipAuthenticationPaths { get; set; } = Array.Empty<string>();
}