using Applications.Interfaces.Services.Authentication;
using Microsoft.Extensions.Options;
using Shared.Constants;
using Shared.Responses;
using System.Text;
using System.Text.Json;

namespace API.Middlewares;

/// <summary>
/// Middleware for HMAC signature validation
/// </summary>
public class SignatureValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<SignatureValidationMiddleware> _logger;
    private readonly AuthenticationOptions _options;

    public SignatureValidationMiddleware(
        RequestDelegate next,
        ILogger<SignatureValidationMiddleware> logger,
        IOptions<AuthenticationOptions> options)
    {
        _next = next;
        _logger = logger;
        _options = options.Value;
    }

    public async Task InvokeAsync(HttpContext context, ISignatureService signatureService)
    {
        // Skip signature validation for non-protected endpoints
        if (ShouldSkipSignatureValidation(context))
        {
            await _next(context);
            return;
        }

        try
        {
            // Get required headers
            var signature = context.Request.Headers["X-Signature"].FirstOrDefault();
            var timestamp = context.Request.Headers["X-Timestamp"].FirstOrDefault();
            var clientId = context.Request.Headers["X-Client-ID"].FirstOrDefault();

            if (string.IsNullOrEmpty(signature))
            {
                await RespondWithError(context, ErrorCodes.BAD_REQUEST_ERROR, 
                    "Missing X-Signature header", 400);
                return;
            }

            if (string.IsNullOrEmpty(timestamp))
            {
                await RespondWithError(context, ErrorCodes.BAD_REQUEST_ERROR, 
                    "Missing X-Timestamp header", 400);
                return;
            }

            if (string.IsNullOrEmpty(clientId))
            {
                await RespondWithError(context, ErrorCodes.BAD_REQUEST_ERROR, 
                    "Missing X-Client-ID header", 400);
                return;
            }

            // Validate timestamp first to prevent replay attacks (if enabled)
            if (_options.EnableTimestampValidation)
            {
                var timestampValidation = signatureService.ValidateTimestamp(timestamp, _options.SignatureToleranceSeconds);
                if (!timestampValidation.IsSuccess || timestampValidation.Data != true)
                {
                    _logger.LogWarning("Invalid timestamp {Timestamp} from client {ClientId}", 
                        timestamp, clientId);
                    
                    await RespondWithError(context, ErrorCodes.BAD_REQUEST_ERROR, 
                        "Invalid or expired timestamp", 400);
                    return;
                }
            }

            // Get partner ID from context (set by JWT middleware)
            var partnerId = context.Items["PartnerId"] as Guid?;
            if (partnerId == null)
            {
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR, 
                    "Partner not authenticated", 401);
                return;
            }

            // Read request body for signature validation
            var requestBody = await GetRequestBodyAsync(context);
            
            // Validate signature
            var signatureValidation = await signatureService.ValidateSignatureAsync(
                partnerId.Value,
                context.Request.Method,
                context.Request.Path,
                timestamp,
                clientId,
                requestBody,
                signature);

            if (!signatureValidation.IsSuccess || signatureValidation.Data?.IsValid != true)
            {
                _logger.LogWarning("Invalid signature from partner {PartnerId}, client {ClientId}", 
                    partnerId, clientId);
                
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR, 
                    signatureValidation.Message ?? "Invalid signature", 401);
                return;
            }

            _logger.LogInformation("Signature validated successfully for partner {PartnerId}", partnerId);

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in signature validation middleware");
            
            await RespondWithError(context, ErrorCodes.INTERNAL_SERVER_ERROR, 
                "Internal server error during signature validation", 500);
        }
    }

    private bool ShouldSkipSignatureValidation(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLower();
        
        if (string.IsNullOrEmpty(path))
            return false;
            
        // Check against configured skip paths
        if (_options.SkipAuthenticationPaths != null)
        {
            foreach (var skipPath in _options.SkipAuthenticationPaths)
            {
                if (path.Contains(skipPath.ToLower()))
                    return true;
            }
        }
        
        // Additional hardcoded paths for basic functionality
        return path.Contains("/swagger") ||
               path.Contains("/health") ||
               path.Contains("/metrics") ||
               path == "/" ||
               path == "/favicon.ico";
    }

    private static async Task<string> GetRequestBodyAsync(HttpContext context)
    {
        // Enable buffering so we can read the body multiple times
        context.Request.EnableBuffering();
        
        using var reader = new StreamReader(context.Request.Body, Encoding.UTF8, true, 1024, true);
        var body = await reader.ReadToEndAsync();
        
        // Reset the position so the next middleware can read it
        context.Request.Body.Position = 0;
        
        return body ?? string.Empty;
    }

    private static async Task RespondWithError(HttpContext context, string errorCode, string message, int statusCode)
    {
        var errorResponse = new Response<object>
        {
            Code = errorCode,
            Message = message,
            TraceId = context.TraceIdentifier
        };

        context.Response.StatusCode = statusCode;
        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse));
    }
}