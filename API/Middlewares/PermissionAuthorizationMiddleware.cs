using Applications.Interfaces.Services.Authorization;
using Microsoft.Extensions.Options;
using Shared.Constants;
using Shared.Responses;
using System.Text.Json;

namespace API.Middlewares;

/// <summary>
/// Middleware for dynamic permission authorization
/// </summary>
public class PermissionAuthorizationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<PermissionAuthorizationMiddleware> _logger;
    private readonly AuthenticationOptions _options;

    public PermissionAuthorizationMiddleware(
        RequestDelegate next,
        ILogger<PermissionAuthorizationMiddleware> logger,
        IOptions<AuthenticationOptions> options)
    {
        _next = next;
        _logger = logger;
        _options = options.Value;
    }

    public async Task InvokeAsync(HttpContext context, IPermissionService permissionService, IConstraintService constraintService)
    {
        // Skip authorization for non-protected endpoints
        if (ShouldSkipAuthorization(context))
        {
            await _next(context);
            return;
        }

        try
        {
            // Get partner ID from context (set by authentication middleware)
            var partnerId = context.Items["PartnerId"] as Guid?;
            if (partnerId == null)
            {
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR, 
                    "Partner not authenticated", 401);
                return;
            }

            // Get permission requirements from endpoint attributes
            var endpoint = context.GetEndpoint();
            var permissionAttributes = endpoint?.Metadata.GetOrderedMetadata<API.Attributes.RequirePermissionAttribute>() ?? 
                Array.Empty<API.Attributes.RequirePermissionAttribute>();
            
            var constraintAttributes = endpoint?.Metadata.GetOrderedMetadata<API.Attributes.RequireConstraintAttribute>() ?? 
                Array.Empty<API.Attributes.RequireConstraintAttribute>();

            // Check permissions
            foreach (var permissionAttr in permissionAttributes)
            {
                var permissionResult = await permissionService.HasPermissionAsync(
                    partnerId.Value, 
                    permissionAttr.FunctionCode, 
                    permissionAttr.PermissionCode);

                if (!permissionResult.IsSuccess || permissionResult.Data?.HasPermission != true)
                {
                    _logger.LogWarning("Permission denied for partner {PartnerId}: {FunctionCode}:{PermissionCode}", 
                        partnerId, permissionAttr.FunctionCode, permissionAttr.PermissionCode);
                    
                    await RespondWithError(context, ErrorCodes.FORBIDDEN_ERROR, 
                        $"Permission denied: {permissionAttr.FunctionCode}:{permissionAttr.PermissionCode}", 403);
                    return;
                }
            }

            // Check constraints
            foreach (var constraintAttr in constraintAttributes)
            {
                var constraintValue = GetConstraintValue(context, constraintAttr);
                
                var constraintResult = await constraintService.ValidateConstraintAsync(
                    partnerId.Value,
                    constraintAttr.ConstraintType,
                    constraintValue);

                if (!constraintResult.IsSuccess || constraintResult.Data?.IsValid != true)
                {
                    _logger.LogWarning("Constraint violation for partner {PartnerId}: {ConstraintType}", 
                        partnerId, constraintAttr.ConstraintType);
                    
                    await RespondWithError(context, ErrorCodes.FORBIDDEN_ERROR, 
                        constraintResult.Message ?? $"Constraint violation: {constraintAttr.ConstraintType}", 403);
                    return;
                }
            }

            _logger.LogDebug("Authorization successful for partner {PartnerId} on {Path}", 
                partnerId, context.Request.Path);

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in permission authorization middleware");
            
            await RespondWithError(context, ErrorCodes.INTERNAL_SERVER_ERROR, 
                "Internal server error during authorization", 500);
        }
    }

    private static bool ShouldSkipAuthorization(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLower();
        
        // Skip authorization for:
        return path != null && (
            path.Contains("/swagger") ||
            path.Contains("/health") ||
            path.Contains("/metrics") ||
            path.Contains("/api/authentication/token") ||
            path.Contains("/api/authentication/validate") ||
            path.Contains("/api/authentication/revoke") ||
            path.Contains("/api/authentication/timestamp") ||
            path.Contains("/api/authentication/ip-check") ||
            path.Contains("/zeninvoice/api/mobi-fone-invoice") || // Skip all MobiFone Invoice APIs (correct path)
            path == "/" ||
            path == "/favicon.ico"
        );
    }

    private static object GetConstraintValue(HttpContext context, API.Attributes.RequireConstraintAttribute constraintAttr)
    {
        // Extract constraint value based on type
        return constraintAttr.ConstraintType switch
        {
            "MONTHLY_LIMIT" => GetMonthlyLimitValue(context),
            "TRANSACTION_SIZE" => GetTransactionSizeValue(context),
            "API_RATE_LIMIT" => 1, // Each request counts as 1
            _ => new object()
        };
    }

    private static object GetMonthlyLimitValue(HttpContext context)
    {
        // Try to extract invoice amount from request body or query parameters
        // This is a simplified example - in practice, you'd parse the actual request
        if (context.Request.Query.TryGetValue("amount", out var amount))
        {
            return decimal.TryParse(amount, out var value) ? value : 0m;
        }
        return 0m;
    }

    private static object GetTransactionSizeValue(HttpContext context)
    {
        // Try to extract transaction count from request
        if (context.Request.Query.TryGetValue("quantity", out var quantity))
        {
            return int.TryParse(quantity, out var value) ? value : 0;
        }
        return 0;
    }

    private static async Task RespondWithError(HttpContext context, string errorCode, string message, int statusCode)
    {
        var errorResponse = new Response<object>
        {
            Code = errorCode,
            Message = message,
            TraceId = context.TraceIdentifier
        };

        context.Response.StatusCode = statusCode;
        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse));
    }
}

