using Applications.Interfaces.Services.Authentication;
using Microsoft.Extensions.Options;
using Shared.Constants;
using Shared.Responses;
using System.Text.Json;
using System.Security.Claims;

namespace API.Middlewares;

/// <summary>
/// Middleware for JWT authentication validation
/// </summary>
public class JwtAuthenticationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<JwtAuthenticationMiddleware> _logger;
    private readonly AuthenticationOptions _options;

    public JwtAuthenticationMiddleware(
        RequestDelegate next,
        ILogger<JwtAuthenticationMiddleware> logger,
        IOptions<AuthenticationOptions> options)
    {
        _next = next;
        _logger = logger;
        _options = options.Value;
    }

    public async Task InvokeAsync(HttpContext context, ITokenService tokenService, IIpWhitelistService ipWhitelistService)
    {
        // Skip authentication for non-protected endpoints
        if (ShouldSkipAuthentication(context))
        {
            await _next(context);
            return;
        }

        try
        {
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
            
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR, 
                    "Missing or invalid Authorization header", 401);
                return;
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            
            // Validate token
            var validationResult = await tokenService.ValidateTokenAsync(token);
            
            if (!validationResult.IsValid)
            {
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR, 
                    validationResult.ErrorMessage ?? "Invalid token", 401);
                return;
            }

            // Extract claims and set user context
            var claims = tokenService.ExtractClaims(token);
            if (claims == null)
            {
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR, 
                    "Unable to extract token claims", 401);
                return;
            }

            var partnerId = tokenService.GetPartnerIdFromClaims(claims);
            if (partnerId == null)
            {
                await RespondWithError(context, ErrorCodes.UNAUTHORIZED_ERROR, 
                    "Invalid partner ID in token", 401);
                return;
            }

            // Check IP whitelist if enabled
            if (_options.EnableIpWhitelist)
            {
                var clientIp = context.Items["ClientIP"]?.ToString() ?? 
                    ipWhitelistService.GetClientIpAddress(context);
                
                var ipValidationResult = await ipWhitelistService.ValidateIpAsync(
                    partnerId.Value, clientIp);
                
                if (!ipValidationResult.IsSuccess || ipValidationResult.Data?.IsAllowed != true)
                {
                    _logger.LogWarning("IP {IP} not whitelisted for partner {PartnerId}", 
                        clientIp, partnerId);
                    
                    await RespondWithError(context, ErrorCodes.FORBIDDEN_ERROR, 
                        "IP address not whitelisted", 403);
                    return;
                }
            }

            // Set user context for the request
            context.User = claims;
            context.Items["PartnerId"] = partnerId.Value;
            context.Items["PartnerName"] = validationResult.PartnerName;
            context.Items["TokenExpires"] = validationResult.ExpiresAt;

            _logger.LogInformation("Partner {PartnerId} authenticated successfully for {Path}", 
                partnerId, context.Request.Path);

            await _next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in JWT authentication middleware");
            
            await RespondWithError(context, ErrorCodes.INTERNAL_SERVER_ERROR, 
                "Internal server error during authentication", 500);
        }
    }

    private bool ShouldSkipAuthentication(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLower();
        
        if (string.IsNullOrEmpty(path))
            return false;
        
        // Debug logging to see actual path
        _logger.LogInformation("JwtAuthenticationMiddleware checking path: {Path}", path);
            
        // Check against configured skip paths
        if (_options.SkipAuthenticationPaths != null)
        {
            foreach (var skipPath in _options.SkipAuthenticationPaths)
            {
                if (path.Contains(skipPath.ToLower()))
                {
                    _logger.LogInformation("Skipping authentication for path: {Path} (matched: {SkipPath})", path, skipPath);
                    return true;
                }
            }
        }
        
        // Additional hardcoded paths for basic functionality
        var shouldSkip = path.Contains("/swagger") ||
                        path.Contains("/health") ||
                        path.Contains("/metrics") ||
                        path == "/" ||
                        path == "/favicon.ico";
                        
        if (shouldSkip)
        {
            _logger.LogInformation("Skipping authentication for path: {Path} (hardcoded rule)", path);
        }
        
        return shouldSkip;
    }

    private static async Task RespondWithError(HttpContext context, string errorCode, string message, int statusCode)
    {
        var errorResponse = new Response<object>
        {
            Code = errorCode,
            Message = message,
            TraceId = context.TraceIdentifier
        };

        context.Response.StatusCode = statusCode;
        context.Response.ContentType = "application/json";
        await context.Response.WriteAsync(JsonSerializer.Serialize(errorResponse));
    }
}