using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace API.Filters;

/// <summary>
/// Swagger Operation Filter để thêm headers cho MobiFone APIs
/// </summary>
public class AddMobiFoneHeadersOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Kiểm tra xem có phải controller MobiFone không
        var controllerName = context.MethodInfo.DeclaringType?.Name;
        var isMobiFoneController = controllerName == "MobiFoneInvoiceController";
        var isAuthenticationController = controllerName == "AuthenticationController";
        
        if (operation.Parameters == null)
        {
            operation.Parameters = new List<OpenApiParameter>();
        }

        // TẮT HOÀN TOÀN authentication headers cho MobiFone APIs
        // (Không thêm bất kỳ header nào cho MobiFone APIs)

        // Thêm HMAC headers cho tất cả protected APIs (trừ authentication endpoints và MobiFone endpoints)
        if (!isAuthenticationController && !isMobiFoneController)
        {
            // X-Timestamp header
            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "X-Timestamp",
                In = ParameterLocation.Header,
                Required = true,
                Schema = new OpenApiSchema { Type = "string" },
                Description = "Unix timestamp (seconds since epoch). Use /api/authentication/timestamp to get current timestamp"
            });

            // X-Signature header
            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "X-Signature",
                In = ParameterLocation.Header,
                Required = true,
                Schema = new OpenApiSchema { Type = "string" },
                Description = "HMAC-SHA256 signature. Format: Base64(HMAC-SHA256(StringToSign, HmacSecret))"
            });

            // X-Client-ID header (case-sensitive to match middleware)
            operation.Parameters.Add(new OpenApiParameter
            {
                Name = "X-Client-ID",
                In = ParameterLocation.Header,
                Required = true,
                Schema = new OpenApiSchema { Type = "string" },
                Description = "Client ID used for HMAC signature calculation"
            });
        }

        // Thêm description về HMAC signature calculation
        if (!isAuthenticationController && !isMobiFoneController)
        {
            var originalDescription = operation.Description ?? "";
            operation.Description = originalDescription + "\n\n" +
                "**HMAC Signature Calculation:**\n" +
                "```\n" +
                "StringToSign = HTTP_METHOD + \"\\n\" + REQUEST_PATH + \"\\n\" + TIMESTAMP + \"\\n\" + CLIENT_ID + \"\\n\" + PAYLOAD\n" +
                "Signature = Base64(HMAC-SHA256(StringToSign, HmacSecret))\n" +
                "```\n" +
                "Where:\n" +
                "- HTTP_METHOD: GET, POST, PUT, DELETE, etc.\n" +
                "- REQUEST_PATH: /api/mobifone-invoice/login\n" +
                "- TIMESTAMP: Unix timestamp from X-Timestamp header\n" +
                "- CLIENT_ID: Value from X-Client-Id header\n" +
                "- PAYLOAD: Request body (empty string for GET requests)\n" +
                "- HmacSecret: Your partner's HMAC secret key";
        }
    }
}