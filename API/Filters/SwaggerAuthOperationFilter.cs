using Microsoft.AspNetCore.Authorization;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace API.Filters;

/// <summary>
/// Filter để loại bỏ Authorization requirement cho các API có [AllowAnonymous]
/// </summary>
public class SwaggerAuthOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Check if the action has [AllowAnonymous] attribute
        var hasAllowAnonymous = context.MethodInfo
            .GetCustomAttributes(typeof(AllowAnonymousAttribute), true)
            .Any();

        // Check if the controller has [AllowAnonymous] attribute
        var controllerHasAllowAnonymous = context.MethodInfo.DeclaringType?
            .GetCustomAttributes(typeof(AllowAnonymousAttribute), true)
            .Any() ?? false;

        // If method or controller has [AllowAnonymous], remove security requirements
        if (hasAllowAnonymous || controllerHasAllowAnonymous)
        {
            operation.Security?.Clear();
        }
    }
}