//using API.Conventions;
//using API.Extensions;
//using API.Middlewares;
//using Applications;
//using Applications.Behaviors;
//using Applications.Features.ClientCredentials.Commands;
//using Applications.Interfaces.Repositories;
//using Applications.Interfaces.Services;
//using Applications.MappingProfiles;
//using Applications.Services.Interfaces;
//using Core.Interfaces;
//using FluentValidation;
//using Infrastructure.DependencyInjections;
//using Infrastructure.Helpers;
//using Infrastructure.Identity;
//using Infrastructure.Persistences;
//using Infrastructure.Persistences.Repositories;
//using Infrastructure.Services;
//using MediatR;
//using Microsoft.AspNetCore.Mvc.ApplicationModels;
//using Microsoft.EntityFrameworkCore;
//using Serilog;
//using Shared.Interfaces;

//var builder = WebApplication.CreateBuilder(args);

//// =============================
//// 📁 Load cấu hình theo môi trường
//// =============================
//builder.Configuration
//    .SetBasePath(Directory.GetCurrentDirectory())
//    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
//    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
//    .AddEnvironmentVariables();

//// =============================
//// 🔧 Logging: Serilog
//// =============================
//builder.Host.UseSerilog((context, services, configuration) =>
//{
//    configuration
//        .ReadFrom.Configuration(context.Configuration)
//        .ReadFrom.Services(services)
//        .Enrich.WithProperty("Application", "ZenPay");
//});

//// =============================
//// 📦 Đăng ký các dịch vụ
//// =============================
//var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
//builder.Services.AddInfrastructureServices(connectionString);
//builder.Services.AddRepositories();

//builder.Services.AddMediatR(cfg =>
//{
//    cfg.RegisterServicesFromAssembly(typeof(ApplicationAssembly).Assembly);
//});
//builder.Services.AddAutoMapper(typeof(MappingProfile).Assembly);

//// External Configuration & Services
//builder.Services.AddScoped<IJwtTokenGenerator, JwtTokenGenerator>();
//builder.Services.AddScoped<IPaginationHelper, PaginationHelper>();
//builder.Services.AddScoped<IPasswordHasher, PasswordHasher>();
//builder.Services.AddScoped<IClientCredentialRepository, ClientCredentialRepository>();
//builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();
//builder.Services.AddHttpContextAccessor();
//builder.Services.AddValidatorsFromAssembly(typeof(CreateClientCredentialCommand).Assembly);
//builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
//builder.Services.AddScoped<INotificationService, NotificationService>();
//builder.Services.AddScoped<IMBFSmsBranchnameService, MBFSmsBranchnameService>();
//builder.Services.AddScoped<ISmsLogRepository, SmsLogRepository>();
//builder.Services.AddScoped<ISmsRetryQueueRepository, SmsRetryQueueRepository>();
//// =============================
//// ⚙️ Cấu hình Controller & JSON
//// =============================
//builder.Services.AddControllers(options =>
//{
//    // ✅ Đặt RouteTokenTransformer tại đây (Controller-level)
//    options.Conventions.Add(new RouteTokenTransformerConvention(new SlugifyParameterTransformer()));
//})
//.AddJsonOptions(options =>
//{
//    // ✅ Đặt camelCase cho JSON (Property & Dictionary keys)
//    options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
//    options.JsonSerializerOptions.DictionaryKeyPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
//});

//// =============================
//// 🌐 CORS
//// =============================
//builder.Services.AddCors(options =>
//{
//    options.AddPolicy("AllowAll", policy =>
//    {
//        policy.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod();
//    });
//});

//// =============================
//// 🔐 JWT Authentication
//// =============================
//builder.Services.AddJwtAuthentication(builder.Configuration);

//// =============================
//// 🧪 Swagger
//// =============================
//builder.Services.AddEndpointsApiExplorer();
//builder.Services.AddSwaggerGen(c =>
//{
//    // Thêm schema xác thực dạng Bearer
//    c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
//    {
//        Name = "Authorization",
//        Type = Microsoft.OpenApi.Models.SecuritySchemeType.Http,
//        Scheme = "bearer",
//        BearerFormat = "JWT",
//        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
//        Description = "Nhập token dạng: **Bearer your-token-here**"
//    });

//    // Áp dụng schema này cho tất cả các API
//    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
//    {
//        {
//            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
//            {
//                Reference = new Microsoft.OpenApi.Models.OpenApiReference
//                {
//                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
//                    Id = "Bearer"
//                }
//            },
//            Array.Empty<string>()
//        }
//    });
//});

//var app = builder.Build();
//// ✅ Đặt TraceIdMiddleware đầu tiên trong middleware pipeline
//app.UseMiddleware<TraceIdMiddleware>();  // Đây là middleware sẽ tạo TraceId cho mỗi request
//// =============================
//// 🌱 Seed database nếu cần
//// =============================
//await app.MigrateAndSeedAsync();

//// =============================
//// 🧱 Middleware pipeline
//// =============================
////if (app.Environment.IsDevelopment())
////{
////    app.UseSwagger();
////    app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "API V1"));
////}
//// ✅ Luôn bật Swagger bất kể môi trường
//app.UseSwagger();
//app.UseSwaggerUI(c =>
//{
//    c.SwaggerEndpoint("/swagger/v1/swagger.json", "API V1");
//});
//app.UseMiddleware<ExceptionHandlingMiddleware>();
//app.UseHttpsRedirection();
//app.UseCors("AllowAll");
//app.UseAuthentication();
//app.UseAuthorization();

//app.MapControllers();
//app.UseMiddleware<SerilogEnrichMiddleware>();

//app.Run();

using API;
using API.Conventions;
using API.Extensions;
using API.Filters;
using API.Middlewares;
using Applications;
using Applications.Behaviors;
using Applications.Features.ClientCredentials.Commands;
using Applications.Features.Notifications.Commands;
using Applications.Features.Notifications.Handlers;
using Applications.Interfaces.Repositories;
using Applications.Interfaces.Services;
using Applications.Interfaces.Services.Authentication;
using Applications.Interfaces.Services.Authorization;
using Applications.Interfaces.Services.Security;
using Applications.MappingProfiles;
using Core.Interfaces;
using FluentValidation;
using Infrastructure.DependencyInjections;
using Infrastructure.Helpers;
using Infrastructure.Identity;
using Infrastructure.Persistences;
using Infrastructure.Persistences.Repositories;
using Infrastructure.Services;
using Infrastructure.Services.Security;
using Infrastructure.Services.Authentication;
using Infrastructure.Services.Authorization;
using MediatR;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.EntityFrameworkCore;
using Serilog;
using Shared.Configs;
using Shared.Exceptions.Handler;
using Shared.Interfaces;
using Shared.Results;

var builder = WebApplication.CreateBuilder(args);

// =============================
// 📁 Load cấu hình theo môi trường
// =============================

builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables();

// =============================
// 🔧 Logging: Serilog
// =============================

builder.Host.UseSerilog((context, services, configuration) =>
{
    configuration
        .ReadFrom.Configuration(context.Configuration)
        .WriteTo.Console()  // Ghi log vào console
        .WriteTo.File("logs/ZenInvoice.log", rollingInterval: RollingInterval.Day);  // Ghi log vào file
});

// =============================
// 📦 Đăng ký các dịch vụ
// =============================

var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddExceptionHandler<CustomExceptionHandler>();
builder.Services.AddInfrastructureServices(connectionString);
builder.Services.AddRepositories();

builder.Services.AddMediatR(cfg =>
{
    cfg.RegisterServicesFromAssembly(typeof(ApplicationAssembly).Assembly);
});
builder.Services.AddAutoMapper(typeof(MappingProfile).Assembly);

// External Configuration & Services
builder.Services.AddScoped<IJwtTokenGenerator, JwtTokenGenerator>();
builder.Services.AddScoped<IPaginationHelper, PaginationHelper>();
builder.Services.AddScoped<IPasswordHasher, PasswordHasher>();
builder.Services.AddScoped<IClientCredentialRepository, ClientCredentialRepository>();
builder.Services.AddScoped<IPartnerRepository, PartnerRepository>();
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>();

// Security services
builder.Services.AddScoped<ISecretEncryptionService, AesSecretEncryptionService>();
builder.Services.AddScoped<SecretMigrationService>();
builder.Services.AddHttpContextAccessor();
builder.Services.AddValidatorsFromAssembly(typeof(CreateClientCredentialCommand).Assembly);
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
builder.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(TraceIdBehavior<,>));
builder.Services.AddScoped<INotificationService, NotificationService>();
// Trong Program.cs hoặc ServiceRegistration.cs
builder.Services.Configure<MBFSMSConfig>(builder.Configuration.GetSection("MBFSMSConfig"));
builder.Services.AddHttpClient<IMBFSmsBranchnameService, MBFSmsBranchnameService>();
builder.Services.AddScoped<ISmsLogRepository, SmsLogRepository>();
builder.Services.AddScoped<ISmsRetryQueueRepository, SmsRetryQueueRepository>();

// MobiFone Invoice Configuration & Services
builder.Services.Configure<Infrastructure.Configurations.MobiFoneInvoiceConfiguration>(
    builder.Configuration.GetSection("MobiFoneInvoice"));
builder.Services.AddHttpClient<Applications.Interfaces.Services.IMobiFoneInvoiceService, Infrastructure.Services.MobiFoneInvoiceService>(client =>
{
    client.Timeout = TimeSpan.FromSeconds(30);
    client.DefaultRequestHeaders.Add("User-Agent", "ZenInvoice/1.0");
});

// =============================
// 🔐 Authentication & Authorization Services
// =============================

// Configuration options - FULL SECURITY ENABLED
builder.Services.Configure<API.Middlewares.AuthenticationOptions>(options =>
{
    options.TokenExpirationMinutes = 120;
    options.SignatureToleranceSeconds = 300; // 5 minutes tolerance for signature timestamp
    options.EnableIpWhitelist = true; // ✅ BẬT IP Whitelist
    options.DefaultHashAlgorithm = "HMAC-SHA256";
    options.EnableUsageTracking = true; // ✅ BẬT Usage Tracking
    options.EnableTimestampValidation = true; // ✅ BẬT Timestamp Validation
    options.SkipAuthenticationPaths = new[] {
        "/api/authentication/token",
        "/api/authentication/validate",
        "/api/authentication/revoke",
        "/api/authentication/create-test-partner",
        "/zeninvoice/api/mobi-fone-invoice", // Skip all MobiFone Invoice APIs (correct path)
        "/swagger",
        "/health"
    };
});

// Authentication Services
builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddScoped<ISignatureService, SignatureService>();
builder.Services.AddScoped<IIpWhitelistService, IpWhitelistService>();

// Authorization Services
builder.Services.AddScoped<IPermissionService, PermissionService>();
builder.Services.AddScoped<IConstraintService, ConstraintService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<IUsageTrackingService, UsageTrackingService>();

// =============================
// ⚙️ Cấu hình Controller & JSON
// =============================

builder.Services.AddControllers(options =>
{
    // ✅ Đặt RouteTokenTransformer tại đây (Controller-level)
    options.Conventions.Add(new RouteTokenTransformerConvention(new SlugifyParameterTransformer()));
})
.AddJsonOptions(options =>
{
    // ✅ Đặt camelCase cho JSON (Property & Dictionary keys)
    options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
    options.JsonSerializerOptions.DictionaryKeyPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
});

// =============================
// 🌐 CORS
// =============================

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod();
    });
});

// =============================
// 🔐 JWT Authentication
// =============================

builder.Services.AddJwtAuthentication(builder.Configuration);

// =============================
// 🧪 Swagger
// =============================

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    // Thêm schema xác thực dạng Bearer
    c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Name = "Authorization",
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Description = "Nhập token dạng: **Bearer your-token-here**"
    });

    // Thêm các headers cho HMAC authentication
    c.AddSecurityDefinition("HMAC", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Name = "X-Signature",
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Description = "HMAC-SHA256 signature của request"
    });

    c.AddSecurityDefinition("Timestamp", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Name = "X-Timestamp",
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Description = "Unix timestamp của request (±5 minutes validity)"
    });

    c.AddSecurityDefinition("ClientId", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Name = "X-Client-Id",
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Description = "Client ID cho HMAC signature"
    });

    // Thêm global parameters cho MobiFone APIs
    c.OperationFilter<AddMobiFoneHeadersOperationFilter>();

    // Filter để loại bỏ Authorization cho APIs có [AllowAnonymous]
    c.OperationFilter<API.Filters.SwaggerAuthOperationFilter>();

    // Áp dụng Bearer token cho tất cả APIs (trừ những API có [AllowAnonymous])
    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
    {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Thêm XML comments nếu có
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

var app = builder.Build();

// ✅ Đặt TraceIdMiddleware đầu tiên trong middleware pipeline
// app.UseMiddleware<TraceIdMiddleware>();  // Đây là middleware sẽ tạo TraceId cho mỗi request

// =============================
// 🔐 Authentication & Authorization Middleware Pipeline
// =============================

// Order is important: IP → JWT → Signature → Permissions → Usage Tracking
app.UseMiddleware<IpWhitelistMiddleware>();
app.UseMiddleware<JwtAuthenticationMiddleware>();
app.UseMiddleware<SignatureValidationMiddleware>();
app.UseMiddleware<PermissionAuthorizationMiddleware>();
app.UseMiddleware<UsageTrackingMiddleware>();

// =============================
// 🌱 Seed database nếu cần
// =============================

await app.MigrateAndSeedAsync();

// =============================
// 🧱 Middleware pipeline
// =============================

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "API V1"));
}

// ✅ Luôn bật Swagger bất kể môi trường
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "API V1");
});

app.UseMiddleware<ExceptionHandlingMiddleware>();
app.UseHttpsRedirection();
app.UseCors("AllowAll");
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.UseMiddleware<SerilogEnrichMiddleware>();

app.Run();
