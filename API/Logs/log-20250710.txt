[2025-07-10 07:08:47 ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5119: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
[2025-07-10 07:10:12 ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://127.0.0.1:5119: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: Address already in use
 ---> System.Net.Sockets.SocketException (48): Address already in use
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.LocalhostListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
[2025-07-10 07:50:14 WRN] Failed to determine the https port for redirect.
[2025-07-10 07:50:14 INF] Attempting to login to MobiFone Invoice API for user: string
[2025-07-10 07:50:14 INF] Calling MobiFone Login API for Username: string - Environment: Test
[2025-07-10 07:50:14 INF] MobiFone Login Response: "OK" - 50 chars
[2025-07-10 07:50:14 INF] MobiFone Login API call completed successfully
[2025-07-10 07:50:49 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-07-10 07:50:49 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-07-10 07:50:49 INF] MobiFone Login Response: "OK" - 343 chars
[2025-07-10 07:50:49 INF] MobiFone Login API call completed successfully
[2025-07-10 09:24:21 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 09:24:21 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJob3NwaXRhbF9pZCI6ImM0OWMwZThmLTM5N2UtNDc5Yi05ODc4LWY2MmE1YmRlZjIyMiIsInVuaXF1ZV9uYW1lIjoidHJ1bmcubnFAZ290cnVzdC52biIsImVtYWlsIjoidHJ1bmcubnFAZ290cnVzdC52biIsImdpdmVuX25hbWUiOiJOZ3V54buFbiBRdeG7kWMgVHJ1bmciLCJuYW1laWQiOiIwMTk3NTdjYy1kMWVhLTcxZTctYWEyZC1mMGI0MDUzNWFhZGMiLCJyb2xlIjpbIkVNQyIsIkVNUiIsIkVNVSIsIkVNRCIsIkVNQSIsIlBNQyIsIlBNUiIsIlBNVSIsIlBNRCIsIlBNQSIsIk1TQyIsIk1TUiIsIk1TVSIsIk1TRCIsIk1TQSIsIlBEQyIsIlBEUiIsIlBEVSIsIlBERCIsIlBEQSIsIlRPQyIsIlRPUiIsIlRPVSIsIlRPRCIsIlRPQSIsIlRSQyIsIlRSUiIsIlRSVSIsIlRSRCIsIlRSQSIsIk1EQyIsIk1EUiIsIk1EVSIsIk1ERCIsIk1EQSIsIlJBQyIsIlJBUiIsIlJBVSIsIlJBRCIsIlJBQSIsIkRTQyIsIkRTUiIsIkRTVSIsIkRTRCIsIkRTQSIsIlNFQyIsIlNFUiIsIlNFVSIsIlNFRCIsIlNFQSIsIkZEQyIsIkZEUiIsIkZEVSIsIkZERCIsIkZEQSIsIkRNQyIsIkRNUiIsIkRNVSIsIkRNRCIsIkRNQSIsIkZNQyIsIkZNUiIsIkZNVSIsIkZNRCIsIkZNQSIsIlNUQyIsIlNUUiIsIlNUVSIsIlNURCIsIlNUQSIsIlF14bqjbiB0cuG7iyB2acOqbiJdLCJuYmYiOjE3NTIwNDE4NDMsImV4cCI6MTc1MjY0MTc4MywiaWF0IjoxNzUyMDQxODQzLCJpc3MiOiJFTVJJc3N1ZXIiLCJhdWQiOiJFTVJBdWRpZW5jZSJ9.aQYxmBXIbBQl-HoNWM62QtT9pEjhRmvWGSDgRm2dQHY;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-08",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": null,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 09:24:21 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 09:24:21 INF] MobiFone CreateInvoice Response: "Unauthorized" - 61 chars
[2025-07-10 09:24:21 WRN] MobiFone CreateInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-07-10 09:24:21 ERR] Error occurred while creating invoice in MobiFone Invoice API: MobiFone CreateInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone CreateInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 112
   at Infrastructure.Services.MobiFoneInvoiceService.CreateInvoiceAsync(SaveListHoadon78Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 327
[2025-07-10 09:24:35 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-07-10 09:24:35 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-07-10 09:24:35 INF] MobiFone Login Response: "OK" - 343 chars
[2025-07-10 09:24:35 INF] MobiFone Login API call completed successfully
[2025-07-10 09:24:45 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-07-10 09:24:45 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-07-10 09:24:45 INF] MobiFone Login Response: "OK" - 343 chars
[2025-07-10 09:24:45 INF] MobiFone Login API call completed successfully
[2025-07-10 09:24:48 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 09:24:48 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer ZmpldXM2ZGdULzhReUV2MkFTRHRPRzJQZnl5QUNUSHpvNTJ6SWJnUHU3ND06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3MzYyNzUyNTIxMTQwOkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-08",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": null,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 09:24:48 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 09:24:51 INF] MobiFone CreateInvoice Response: "OK" - 2840 chars
[2025-07-10 09:24:51 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 09:24:51 INF] Successfully created invoice with response: 1 items
[2025-07-10 14:38:43 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 14:38:43 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5Q32Y_A4FZvlKpf1_7XVTRE0FSNodWJ2AbRKK3VTVGw;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-08",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": null,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 14:38:43 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 14:38:43 INF] MobiFone CreateInvoice Response: "Unauthorized" - 61 chars
[2025-07-10 14:38:43 WRN] MobiFone CreateInvoice API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-07-10 14:38:43 ERR] Error occurred while creating invoice in MobiFone Invoice API: MobiFone CreateInvoice API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone CreateInvoice API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 112
   at Infrastructure.Services.MobiFoneInvoiceService.CreateInvoiceAsync(SaveListHoadon78Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 327
[2025-07-10 14:38:53 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-07-10 14:38:53 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-07-10 14:38:53 INF] MobiFone Login Response: "OK" - 343 chars
[2025-07-10 14:38:53 INF] MobiFone Login API call completed successfully
[2025-07-10 14:39:14 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 14:39:14 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-08",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": null,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 14:39:14 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 14:39:18 INF] MobiFone CreateInvoice Response: "OK" - 2840 chars
[2025-07-10 14:39:18 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 14:39:18 INF] Successfully created invoice with response: 1 items
[2025-07-10 14:42:04 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 14:42:04 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-08",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": null,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 14:42:04 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 14:42:49 INF] MobiFone CreateInvoice Response: "OK" - 2840 chars
[2025-07-10 14:42:49 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 14:42:49 INF] Successfully created invoice with response: 1 items
[2025-07-10 14:43:43 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 14:43:43 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-08",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": null,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 14:43:43 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 15:22:32 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 15:22:32 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-08",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": 0,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 15:22:32 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 15:22:38 INF] MobiFone CreateInvoice Response: "OK" - 2837 chars
[2025-07-10 15:22:38 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 15:22:38 INF] Successfully created invoice with response: 1 items
[2025-07-10 15:24:00 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 15:24:00 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-08",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": 0,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 15:24:00 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 15:24:04 INF] MobiFone CreateInvoice Response: "OK" - 2837 chars
[2025-07-10 15:24:04 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 15:24:04 INF] Successfully created invoice with response: 1 items
[2025-07-10 15:24:42 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 15:24:42 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-08",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": 0,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 15:24:42 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 15:25:55 INF] MobiFone CreateInvoice Response: "OK" - 2837 chars
[2025-07-10 15:25:55 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 15:25:55 INF] Successfully created invoice with response: 1 items
[2025-07-10 15:27:28 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 15:27:28 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-10",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": 0,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 15:27:28 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 15:30:15 INF] MobiFone CreateInvoice Response: "OK" - 2837 chars
[2025-07-10 15:30:15 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 15:30:15 INF] Successfully created invoice with response: 1 items
[2025-07-10 16:49:11 INF] IP validation prepared for ::1 on /zenInvoice/api/mobi-fone-invoice/login
[2025-07-10 16:49:19 INF] IP validation prepared for ::1 on /zenInvoice/api/mobi-fone-invoice/login
[2025-07-10 16:49:20 INF] IP validation prepared for ::1 on /zenInvoice/api/mobi-fone-invoice/login
[2025-07-10 16:49:20 INF] IP validation prepared for ::1 on /zenInvoice/api/mobi-fone-invoice/login
[2025-07-10 16:49:22 INF] IP validation prepared for ::1 on /zenInvoice/api/mobi-fone-invoice/login
[2025-07-10 16:49:26 INF] IP validation prepared for ::1 on /zenInvoice/api/mobi-fone-invoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong
[2025-07-10 16:53:22 INF] JwtAuthenticationMiddleware checking path: /swagger
[2025-07-10 16:53:22 INF] Skipping authentication for path: /swagger (matched: /swagger)
[2025-07-10 16:53:22 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-07-10 16:53:22 INF] Skipping authentication for path: /swagger/v1/swagger.json (matched: /swagger)
[2025-07-10 16:53:30 INF] IP validation prepared for ::1 on /zenInvoice/api/mobi-fone-invoice/login
[2025-07-10 16:53:30 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/login
[2025-07-10 16:53:31 INF] IP validation prepared for ::1 on /zenInvoice/api/mobi-fone-invoice/login
[2025-07-10 16:53:31 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/login
[2025-07-10 16:56:13 INF] JwtAuthenticationMiddleware checking path: /swagger
[2025-07-10 16:56:13 INF] Skipping authentication for path: /swagger (matched: /swagger)
[2025-07-10 16:56:13 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-07-10 16:56:13 INF] Skipping authentication for path: /swagger/v1/swagger.json (matched: /swagger)
[2025-07-10 16:56:20 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/login
[2025-07-10 16:56:20 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/login (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-10 16:56:20 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-07-10 16:56:20 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-07-10 16:56:20 INF] MobiFone Login Response: "OK" - 343 chars
[2025-07-10 16:56:20 INF] MobiFone Login API call completed successfully
[2025-07-10 16:56:25 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong
[2025-07-10 16:56:25 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-10 16:56:25 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 16:56:25 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-10",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": 0,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 16:56:25 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 16:56:28 INF] MobiFone CreateInvoice Response: "OK" - 2837 chars
[2025-07-10 16:56:28 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 16:56:28 INF] Successfully created invoice with response: 1 items
[2025-07-10 16:57:01 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong
[2025-07-10 16:57:01 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-10 16:57:01 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 16:57:01 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-11",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": 0,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 16:57:01 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 16:57:02 INF] MobiFone CreateInvoice Response: "OK" - 87 chars
[2025-07-10 16:57:02 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 16:57:02 INF] Successfully created invoice with response: 1 items
[2025-07-10 16:59:11 INF] JwtAuthenticationMiddleware checking path: /swagger
[2025-07-10 16:59:11 INF] Skipping authentication for path: /swagger (matched: /swagger)
[2025-07-10 16:59:11 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-07-10 16:59:11 INF] Skipping authentication for path: /swagger/v1/swagger.json (matched: /swagger)
[2025-07-10 16:59:14 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong
[2025-07-10 16:59:14 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-10 16:59:14 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 16:59:14 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-11",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": 0,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 16:59:14 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 16:59:16 INF] MobiFone CreateInvoice Response: "OK" - 87 chars: [{"error":"P0001: Ngày hóa đơn/biên lai/chứng từ không hợp lệ. Vui lòng kiểm tra lại"}]
[2025-07-10 16:59:16 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 16:59:16 INF] Successfully created invoice with response: 1 items
[2025-07-10 16:59:39 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong
[2025-07-10 16:59:39 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-10 16:59:39 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 16:59:39 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-11",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": 0,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 16:59:39 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 16:59:40 INF] MobiFone CreateInvoice Response: "OK" - 87 chars: [{"error":"P0001: Ngày hóa đơn/biên lai/chứng từ không hợp lệ. Vui lòng kiểm tra lại"}]
[2025-07-10 17:00:46 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 17:08:31 INF] JwtAuthenticationMiddleware checking path: /swagger
[2025-07-10 17:08:31 INF] Skipping authentication for path: /swagger (matched: /swagger)
[2025-07-10 17:08:31 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-07-10 17:08:31 INF] Skipping authentication for path: /swagger/v1/swagger.json (matched: /swagger)
[2025-07-10 17:08:34 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong
[2025-07-10 17:08:34 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/tao-moi-hoa-don-gtgt-quy-trinh-thuong (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-10 17:08:34 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-10 17:08:34 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer bXBVcHZnalVYMEhJc04wNWdhV2FRWU1lVDNmNVVNUXBYY01SSjJIMmptOD06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc3NTUxMzM2MzI4MzM1OkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50000,
              "tthue": 2000,
              "tgtien": 62000,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-11",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": null,
      "tthdon": 0,
      "mccqthue": null,
      "sbmat": null,
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": null,
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": null,
      "mchang": null,
      "mdvqhnsach_mua": null,
      "shchieu": null,
      "tgtcthue": 50000,
      "tgtthue": 2000,
      "tgtttbso": 62000,
      "tgtttbso_last": 62000,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": null,
      "lhdclquan": null,
      "khmshdclquan": null,
      "khhdclquan": null,
      "shdclquan": null,
      "nlhdclquan": null
    }
  ]
}' 
[2025-07-10 17:08:34 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-10 17:08:35 INF] MobiFone CreateInvoice Response: "OK" - 87 chars: [{"error":"P0001: Ngày hóa đơn/biên lai/chứng từ không hợp lệ. Vui lòng kiểm tra lại"}]
[2025-07-10 17:08:58 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-10 17:08:58 INF] Successfully created invoice with response: 1 items
[2025-07-10 19:51:17 INF] JwtAuthenticationMiddleware checking path: /swagger
[2025-07-10 19:51:17 INF] Skipping authentication for path: /swagger (matched: /swagger)
[2025-07-10 19:51:17 INF] JwtAuthenticationMiddleware checking path: /swagger/favicon-32x32.png
[2025-07-10 19:51:17 INF] Skipping authentication for path: /swagger/favicon-32x32.png (matched: /swagger)
[2025-07-10 19:51:17 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-07-10 19:51:17 INF] Skipping authentication for path: /swagger/v1/swagger.json (matched: /swagger)
