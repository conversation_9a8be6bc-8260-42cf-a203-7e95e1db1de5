[2025-07-13 12:27:02 INF] JwtAuthenticationMiddleware checking path: /swagger
[2025-07-13 12:27:02 INF] Skipping authentication for path: /swagger (matched: /swagger)
[2025-07-13 12:27:02 INF] JwtAuthenticationMiddleware checking path: /swagger/favicon-16x16.png
[2025-07-13 12:27:02 INF] Skipping authentication for path: /swagger/favicon-16x16.png (matched: /swagger)
[2025-07-13 12:27:02 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-07-13 12:27:02 INF] Skipping authentication for path: /swagger/v1/swagger.json (matched: /swagger)
[2025-07-13 12:27:04 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-07-13 12:27:04 INF] Skipping authentication for path: /swagger/v1/swagger.json (matched: /swagger)
[2025-07-13 12:27:32 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/login
[2025-07-13 12:27:32 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/login (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-13 12:27:32 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-07-13 12:27:32 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-07-13 12:27:33 INF] MobiFone Login Response: "OK" - 343 chars: {"isWeakPass":false,"token":"OFZEVU81aEgzZmsvWUxaSlJ4ZlhRQ3gvaC9nKzRGOFBEMlU2d1RXMlRKZz06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODgwMDY0NTMyODM4MzYyOkNUS1Yy","refresh_token":"6f5ea18b-8b28-47a1-a19e-a056ca856554","ma_dvcs":"CTKV2","wb_user_id":"2376a8cb-a17a-4da9-82d4-e4dca86b3667","noti_sso":"0","notes_sso":"0","notify":"0","is_link_sso":true}
[2025-07-13 12:27:33 INF] MobiFone Login API call completed successfully
[2025-07-13 13:40:43 INF] JwtAuthenticationMiddleware checking path: /swagger
[2025-07-13 13:40:43 INF] Skipping authentication for path: /swagger (matched: /swagger)
[2025-07-13 13:40:43 ERR] Connection id "0HNE1R0OCN29O", Request id "0HNE1R0OCN29O:00000003": An unhandled exception was thrown by the application.
System.InvalidOperationException: Encryption key not found. Set Security:EncryptionKey in configuration or ZENINVOICE_ENCRYPTION_KEY environment variable.
   at Infrastructure.Services.Security.AesSecretEncryptionService..ctor(IConfiguration configuration, ILogger`1 logger) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/Security/AesSecretEncryptionService.cs:line 26
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.GetService(IServiceProvider sp, Type type, Type middleware)
   at lambda_method17(Closure, Object, HttpContext, IServiceProvider)
   at API.Middlewares.JwtAuthenticationMiddleware.InvokeAsync(HttpContext context, ITokenService tokenService, IIpWhitelistService ipWhitelistService) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/JwtAuthenticationMiddleware.cs:line 34
   at API.Middlewares.IpWhitelistMiddleware.InvokeAsync(HttpContext context, IIpWhitelistService ipWhitelistService) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/API/Middlewares/IpWhitelistMiddleware.cs:line 33
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http.HttpProtocol.ProcessRequests[TContext](IHttpApplication`1 application)
