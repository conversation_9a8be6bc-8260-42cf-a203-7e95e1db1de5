[2025-07-07 21:03:19 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-07-07 21:03:19 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-07-07 21:03:23 INF] MobiFone Login Response: "OK" - 343 chars
[2025-07-07 21:03:23 INF] MobiFone Login API call completed successfully
[2025-07-07 22:27:34 INF] Creating invoice with EditMode: 1, Data count: 1
[2025-07-07 22:27:35 INF] Generated cURL command for MobiFone CreateInvoice API: curl -X POST 'http://mobiinvoice.vn:9000/api/Invoice68/SaveListHoadon78' -H 'User-Agent: ZenInvoice/1.0' -H 'Authorization: Bearer R2dyQitSQmFnQy9yQ05jWE05S08raGhGYklITVNmckIzaGMvcGJQQjBNbz06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc1MTg5OTk0MzQ2NDczOkNUS1Yy;CTKV2' -H 'Content-Type: application/json; charset=utf-8' -d '{
  "editmode": 1,
  "data": [
    {
      "TGTKCThue": null,
      "TGTKhac": null,
      "details": [
        {
          "data": [
            {
              "tsuat": "4",
              "stt": "1",
              "ma": "Tra sua",
              "ten": "Ly tr\u00E0 s\u1EEFa l\u00E0i",
              "mdvtinh": "Ly",
              "dvtinh": null,
              "dgia": 50000,
              "sluong": 1,
              "tlckhau": 0,
              "stckhau": 0,
              "thtien": 50001,
              "tthue": 2000,
              "tgtien": 52001,
              "kmai": 1,
              "lhhdthu": null,
              "skhung": null,
              "smay": null,
              "bksptvchuyen": null,
              "tnghang": null,
              "dcnghang": null,
              "mstnghang": null,
              "mddnghang": null
            }
          ]
        }
      ],
      "hoadon68_phi": null,
      "hoadon68_khac": null,
      "cctbao_id": "791f4ff1-ce04-4941-9ad6-5343401ca6c1",
      "hdon_id": null,
      "nlap": "2025-07-03",
      "sdhang": "",
      "khieu": "1C25MYY",
      "shdon": 0,
      "tthai": "",
      "tthdon": 0,
      "mccqthue": "",
      "sbmat": "",
      "dvtte": "VND",
      "tgia": 1,
      "htttoan": "Ti\u1EC1n m\u1EB7t/Chuy\u1EC3n kho\u1EA3n",
      "sdtnban": "",
      "stknban": "",
      "tnhban": "",
      "mnmua": "",
      "mst": "",
      "tnmua": "",
      "email": "",
      "ten": "",
      "dchi": "",
      "stknmua": "",
      "sdtnmua": "",
      "tnhmua": "",
      "tchang": "",
      "mchang": "",
      "mdvqhnsach_mua": "",
      "shchieu": "",
      "tgtcthue": 50001,
      "tgtthue": 2000,
      "tgtttbso": 52001,
      "tgtttbso_last": 52001,
      "tkcktmn": 0,
      "tgtphi": 0,
      "mdvi": "TRUNGDEMO",
      "is_hdcma": 1,
      "hdon_id_old": "",
      "lhdclquan": 1,
      "khmshdclquan": "",
      "khhdclquan": "",
      "shdclquan": "",
      "nlhdclquan": ""
    }
  ]
}' 
[2025-07-07 22:27:35 INF] Calling MobiFone CreateInvoice API - EditMode: 1
[2025-07-07 22:27:52 INF] MobiFone CreateInvoice Response: "OK" - 2857 chars
[2025-07-07 22:27:52 INF] MobiFone CreateInvoice API call completed successfully
[2025-07-07 22:27:52 INF] Successfully created invoice with response: 1 items
[2025-07-07 22:28:16 INF] SignInvoiceCertFile68 with Data count: 1
[2025-07-07 22:28:16 INF] Calling MobiFone SignInvoiceCertFile68 API - Data count: 1
[2025-07-07 22:28:18 INF] MobiFone SignInvoiceCertFile68 Response: "Unauthorized" - 61 chars
[2025-07-07 22:28:18 WRN] MobiFone SignInvoiceCertFile68 API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-07-07 22:28:18 ERR] Error occurred while executing SignInvoiceCertFile68 in MobiFone Invoice API: MobiFone SignInvoiceCertFile68 API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone SignInvoiceCertFile68 API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 112
   at Infrastructure.Services.MobiFoneInvoiceService.SignInvoiceCertFile68Async(SignInvoiceCertFile68Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 830
[2025-07-07 22:28:22 INF] SignInvoiceCertFile68 with Data count: 1
[2025-07-07 22:28:22 INF] Calling MobiFone SignInvoiceCertFile68 API - Data count: 1
[2025-07-07 22:28:39 INF] MobiFone SignInvoiceCertFile68 Response: "Unauthorized" - 61 chars
[2025-07-07 22:28:39 WRN] MobiFone SignInvoiceCertFile68 API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-07-07 22:28:39 ERR] Error occurred while executing SignInvoiceCertFile68 in MobiFone Invoice API: MobiFone SignInvoiceCertFile68 API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone SignInvoiceCertFile68 API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 112
   at Infrastructure.Services.MobiFoneInvoiceService.SignInvoiceCertFile68Async(SignInvoiceCertFile68Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 830
