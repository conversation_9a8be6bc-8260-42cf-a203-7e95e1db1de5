[2025-07-11 13:38:03 INF] JwtAuthenticationMiddleware checking path: /swagger
[2025-07-11 13:38:03 INF] Skipping authentication for path: /swagger (matched: /swagger)
[2025-07-11 13:38:03 INF] JwtAuthenticationMiddleware checking path: /swagger/favicon-32x32.png
[2025-07-11 13:38:03 INF] Skipping authentication for path: /swagger/favicon-32x32.png (matched: /swagger)
[2025-07-11 13:38:03 INF] JwtAuthenticationMiddleware checking path: /swagger/v1/swagger.json
[2025-07-11 13:38:03 INF] Skipping authentication for path: /swagger/v1/swagger.json (matched: /swagger)
[2025-07-11 13:39:38 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf
[2025-07-11 13:39:38 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-11 13:39:38 INF] DownloadInvoicePDF for invoice ID: d6a1261b-9558-42f4-a23b-8dc2c953cc18, type: PDF, inchuyendoi: false
[2025-07-11 13:39:38 WRN] Failed to download PDF for invoice d6a1261b-9558-42f4-a23b-8dc2c953cc18. Status: "Unauthorized", Content: {"Message":"Authorization has been denied for this request."}
[2025-07-11 13:40:17 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf
[2025-07-11 13:40:17 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-11 13:40:17 INF] DownloadInvoicePDF for invoice ID: d6a1261b-9558-42f4-a23b-8dc2c953cc18, type: PDF, inchuyendoi: false
[2025-07-11 13:40:21 INF] Successfully downloaded PDF for invoice d6a1261b-9558-42f4-a23b-8dc2c953cc18, size: 1295304 bytes
[2025-07-11 13:44:55 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/login
[2025-07-11 13:44:55 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/login (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-11 13:44:55 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-07-11 13:44:55 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-07-11 13:44:55 INF] MobiFone Login Response: "OK" - 343 chars: {"isWeakPass":false,"token":"UW14Z0hRQ3FzTXo3U0tJNzZnbCtDalFtdjExMCs0elhKV3RZYldLZW5YQT06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc4MzgyOTU4MTYyNTExOkNUS1Yy","refresh_token":"e96b7f30-395f-4f7d-be22-741fb35907f1","ma_dvcs":"CTKV2","wb_user_id":"2376a8cb-a17a-4da9-82d4-e4dca86b3667","noti_sso":"0","notes_sso":"0","notify":"0","is_link_sso":true}
[2025-07-11 13:44:55 INF] MobiFone Login API call completed successfully
[2025-07-11 13:45:38 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf
[2025-07-11 13:45:38 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-11 13:45:38 INF] DownloadInvoicePDF for invoice ID: d7368b0f-a415-4809-8685-f3e907b6cd08, type: PDF, inchuyendoi: false
[2025-07-11 13:45:42 INF] Successfully downloaded PDF for invoice d7368b0f-a415-4809-8685-f3e907b6cd08, size: 1295307 bytes
[2025-07-11 14:27:39 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/certificates
[2025-07-11 14:27:39 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/certificates (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-11 14:27:39 INF] Getting list certificates file 68
[2025-07-11 14:27:39 INF] Calling MobiFone GetListCertificatesFile68 API
[2025-07-11 14:27:39 INF] MobiFone GetListCertificatesFile68 Response: "Unauthorized" - 61 chars: {"Message":"Authorization has been denied for this request."}
[2025-07-11 14:27:39 WRN] MobiFone GetListCertificatesFile68 API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-07-11 14:27:39 ERR] Error occurred while getting certificates from MobiFone Invoice API: MobiFone GetListCertificatesFile68 API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetListCertificatesFile68 API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 114
   at Infrastructure.Services.MobiFoneInvoiceService.GetListCertificatesFile68Async(GetListCertificatesFile68Request request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 278
[2025-07-11 14:27:47 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/data-references
[2025-07-11 14:27:47 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/data-references (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-11 14:27:47 INF] Getting data references with RefId: RF00059
[2025-07-11 14:27:47 INF] Calling MobiFone GetDataReferences API - RefId: RF00059
[2025-07-11 14:27:47 INF] MobiFone GetDataReferences Response: "Unauthorized" - 61 chars: {"Message":"Authorization has been denied for this request."}
[2025-07-11 14:27:47 WRN] MobiFone GetDataReferences API returned non-success status: "Unauthorized" - {"Message":"Authorization has been denied for this request."}
[2025-07-11 14:27:47 ERR] Error occurred while getting data references from MobiFone Invoice API: MobiFone GetDataReferences API trả về mã lỗi 401
Applications.Exceptions.MobiFoneApiResponseException: MobiFone GetDataReferences API trả về mã lỗi 401
   at Infrastructure.Services.MobiFoneInvoiceService.HandleMobiFoneApiResponse[T](HttpResponseMessage response, String apiName, String endpoint) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 114
   at Infrastructure.Services.MobiFoneInvoiceService.GetDataReferencesAsync(GetDataReferencesRequest request, String token, String maDvcs, CancellationToken cancellationToken) in /Users/<USER>/Workspace/Working/OutSrc/ZenInvoice/Infrastructure/Services/MobiFoneInvoiceService.cs:line 235
[2025-07-11 14:27:59 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/data-references
[2025-07-11 14:27:59 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/data-references (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-11 14:27:59 INF] Getting data references with RefId: RF00059
[2025-07-11 14:27:59 INF] Calling MobiFone GetDataReferences API - RefId: RF00059
[2025-07-11 14:27:59 INF] MobiFone GetDataReferences Response: "OK" - 87229 chars: [{"qlmtke_id":"ba75563c-a417-42f1-bfa8-c171ac5fb0c5","qlkhsdung_id":"21cf028a-92ac-4040-93ec-44d2cf9bedc6","lhdon":null,"hthuc":null,"khdon":null,"khhdon":"CB-25T","sdmau":1,"code":"CTKV2","nglap":"DEMO","nlap":"2025-01-03T11:25:53.45319","loaikh":2,"tenbl":null,"loaibl":"02","solienbl":null,"sottbl":null,"htbl":"T","khmbl":"02BLP0-001","tinh_code":null,"mtttien":null,"permission_id":"f18f2f9f-a1a4-4f33-9a79-26031e298c36","qlkhsdung_id1":"21cf028a-92ac-4040-93ec-44d2cf9bedc6","wb_user_id":"2376a8cb-a17a-4da9-82d4-e4dca86b3667","value":"CB-25T","id":"21cf028a-92ac-4040-93ec-44d2cf9bedc6"},{"qlmtke_id":"ba75563c-a417-42f1-bfa8-c171ac5fb0c5","qlkhsdung_id":"3f8d9827-fbd6-40b6-9e70-8d7fd78ca4a8","lhdon":null,"hthuc":null,"khdon":null,"khhdon":"CB-24T","sdmau":1,"code":"CTKV2","nglap":"DEMO","nlap":"2024-08-26T15:25:34","loaikh":2,"tenbl":null,"loaibl":"02","solienbl":null,"sottbl":null,"htbl":"T","khmbl":"02BLP0-001","tinh_code":null,"mtttien":null,"permission_id":"a2273320-b374-427e-95b2-
[2025-07-11 14:27:59 INF] MobiFone GetDataReferences API call completed successfully
[2025-07-11 14:27:59 INF] Successfully retrieved 147 invoice templates
[2025-07-11 14:50:51 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf
[2025-07-11 14:50:51 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-11 14:50:51 INF] DownloadInvoicePDF for invoice ID: 16128e90-aa0a-4ab1-b22a-0ff2b035ac1c, type: PDF, inchuyendoi: false
[2025-07-11 14:50:51 WRN] Failed to download PDF for invoice 16128e90-aa0a-4ab1-b22a-0ff2b035ac1c. Status: "Unauthorized", Content: {"Message":"Authorization has been denied for this request."}
[2025-07-11 14:50:59 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/login
[2025-07-11 14:50:59 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/login (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-11 14:50:59 INF] Attempting to login to MobiFone Invoice API for user: <EMAIL>
[2025-07-11 14:50:59 INF] Calling MobiFone Login API for Username: <EMAIL> - Environment: Test
[2025-07-11 14:50:59 INF] MobiFone Login Response: "OK" - 343 chars: {"isWeakPass":false,"token":"TlpBVlR4by9yekVjL1luMDZGaWRQazlTejQ3dDRpQ055cGRVcDluSWF5bz06VFJVTkdRVU9DTkdVWUBHTUFJTC5DT006NjM4ODc4NDIyNTk0MjUwNzg0OkNUS1Yy","refresh_token":"29e581cd-5205-4664-89a6-dfed29e21e70","ma_dvcs":"CTKV2","wb_user_id":"2376a8cb-a17a-4da9-82d4-e4dca86b3667","noti_sso":"0","notes_sso":"0","notify":"0","is_link_sso":true}
[2025-07-11 14:50:59 INF] MobiFone Login API call completed successfully
[2025-07-11 14:51:12 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf
[2025-07-11 14:51:12 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-11 14:51:12 INF] DownloadInvoicePDF for invoice ID: 16128e90-aa0a-4ab1-b22a-0ff2b035ac1c, type: PDF, inchuyendoi: false
[2025-07-11 14:51:16 INF] Successfully downloaded PDF for invoice 16128e90-aa0a-4ab1-b22a-0ff2b035ac1c, size: 176626 bytes
[2025-07-11 16:07:13 INF] JwtAuthenticationMiddleware checking path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf
[2025-07-11 16:07:13 INF] Skipping authentication for path: /zeninvoice/api/mobi-fone-invoice/download-invoice-pdf (matched: /zeninvoice/api/mobi-fone-invoice)
[2025-07-11 16:07:13 INF] DownloadInvoicePDF for invoice ID: 16128e90-aa0a-4ab1-b22a-0ff2b035ac1c, type: PDF, inchuyendoi: false
[2025-07-11 16:07:14 INF] Successfully downloaded PDF for invoice 16128e90-aa0a-4ab1-b22a-0ff2b035ac1c, size: 180855 bytes
