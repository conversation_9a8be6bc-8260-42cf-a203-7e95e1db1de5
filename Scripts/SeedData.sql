-- =================================================================
-- ZenInvoice Authentication & Authorization Seed Data
-- =================================================================
-- Script để insert dữ liệu mẫu cho hệ thống authentication/authorization

-- =================================================================
-- 1. FUNCTIONS - Các chức năng hệ thống
-- =================================================================
INSERT INTO "Functions" ("Id", "FunctionCode", "FunctionName", "Description", "Category", "IsActive", "CreatedAt", "UpdatedAt", "CreatedBy") VALUES
(gen_random_uuid(), 'INVOICE_CREATE', 'Tạo hóa đơn', 'Chức năng tạo hóa đơn điện tử', 'INVOICE', true, NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'INVOICE_READ', 'Xem hóa đơn', 'Chức năng xem thông tin hóa đơn', 'INVOICE', true, NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'INVOICE_UPDATE', 'Cập nhật hóa đơn', 'Chức năng cập nhật thông tin hóa đơn', 'INVOICE', true, NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'INVOICE_DELETE', 'Xóa hóa đơn', 'Chức năng xóa hóa đơn', 'INVOICE', true, NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'INVOICE_SIGN', 'Ký hóa đơn', 'Chức năng ký số hóa đ�nh', 'INVOICE', true, NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'INVOICE_SEND_CQT', 'Gửi CQT', 'Chức năng gửi hóa đơn lên Cục Quản lý thuế', 'INVOICE', true, NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'MOBIFONE_API', 'MobiFone API', 'Chức năng gọi API MobiFone', 'INTEGRATION', true, NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'SMS_SEND', 'Gửi SMS', 'Chức năng gửi tin nhắn SMS', 'NOTIFICATION', true, NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'REPORT_VIEW', 'Xem báo cáo', 'Chức năng xem báo cáo thống kê', 'REPORT', true, NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'ADMIN_MANAGE', 'Quản trị hệ thống', 'Chức năng quản trị toàn hệ thống', 'ADMIN', true, NOW(), NOW(), gen_random_uuid());

-- =================================================================
-- 2. PERMISSIONS - Các quyền hạn
-- =================================================================
INSERT INTO "Permissions" ("Id", "PermissionCode", "PermissionName", "Description", "CreatedAt", "UpdatedAt", "CreatedBy") VALUES
(gen_random_uuid(), 'CREATE', 'Tạo mới', 'Quyền tạo mới dữ liệu', NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'READ', 'Đọc', 'Quyền đọc/xem dữ liệu', NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'UPDATE', 'Cập nhật', 'Quyền cập nhật dữ liệu', NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'DELETE', 'Xóa', 'Quyền xóa dữ liệu', NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'EXECUTE', 'Thực thi', 'Quyền thực thi chức năng', NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'APPROVE', 'Phê duyệt', 'Quyền phê duyệt', NOW(), NOW(), gen_random_uuid());

-- =================================================================
-- 3. FUNCTION PERMISSIONS - Mapping Functions và Permissions
-- =================================================================
INSERT INTO "FunctionPermissions" ("Id", "FunctionId", "PermissionId", "IsRequired", "CreatedAt", "UpdatedAt", "CreatedBy")
SELECT 
    gen_random_uuid(),
    f."Id",
    p."Id",
    true,
    NOW(),
    NOW(),
    gen_random_uuid()
FROM "Functions" f
CROSS JOIN "Permissions" p
WHERE 
    (f."FunctionCode" = 'INVOICE_CREATE' AND p."PermissionCode" IN ('CREATE', 'EXECUTE')) OR
    (f."FunctionCode" = 'INVOICE_READ' AND p."PermissionCode" = 'READ') OR
    (f."FunctionCode" = 'INVOICE_UPDATE' AND p."PermissionCode" IN ('UPDATE', 'READ')) OR
    (f."FunctionCode" = 'INVOICE_DELETE' AND p."PermissionCode" IN ('DELETE', 'READ')) OR
    (f."FunctionCode" = 'INVOICE_SIGN' AND p."PermissionCode" IN ('EXECUTE', 'UPDATE')) OR
    (f."FunctionCode" = 'INVOICE_SEND_CQT' AND p."PermissionCode" IN ('EXECUTE', 'UPDATE')) OR
    (f."FunctionCode" = 'MOBIFONE_API' AND p."PermissionCode" = 'EXECUTE') OR
    (f."FunctionCode" = 'SMS_SEND' AND p."PermissionCode" = 'EXECUTE') OR
    (f."FunctionCode" = 'REPORT_VIEW' AND p."PermissionCode" = 'READ') OR
    (f."FunctionCode" = 'ADMIN_MANAGE' AND p."PermissionCode" IN ('CREATE', 'READ', 'UPDATE', 'DELETE', 'EXECUTE', 'APPROVE'));

-- =================================================================
-- 4. PARTNER ROLES - Các vai trò đối tác
-- =================================================================
INSERT INTO "PartnerRoles" ("Id", "Code", "Name", "Description", "Priority", "IsActive", "DefaultApiRateLimitPerHour", "DefaultMonthlyInvoiceLimit", "Metadata", "CreatedAt", "UpdatedAt", "CreatedBy") VALUES
(gen_random_uuid(), 'ADMIN', 'Quản trị viên', 'Vai trò quản trị viên có toàn quyền', 1000, true, 10000, 1000000, '{"level": "admin", "canManageUsers": true}', NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'PREMIUM_PARTNER', 'Đối tác cao cấp', 'Đối tác cao cấp với nhiều quyền hạn', 800, true, 5000, 500000, '{"level": "premium", "supportPriority": "high"}', NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'STANDARD_PARTNER', 'Đối tác tiêu chuẩn', 'Đối tác tiêu chuẩn với quyền hạn cơ bản', 500, true, 2000, 100000, '{"level": "standard", "supportPriority": "normal"}', NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'BASIC_PARTNER', 'Đối tác cơ bản', 'Đối tác cơ bản với quyền hạn hạn chế', 200, true, 1000, 50000, '{"level": "basic", "supportPriority": "low"}', NOW(), NOW(), gen_random_uuid()),
(gen_random_uuid(), 'TRIAL_PARTNER', 'Đối tác dùng thử', 'Đối tác dùng thử với quyền hạn rất hạn chế', 100, true, 100, 1000, '{"level": "trial", "expirationDays": 30}', NOW(), NOW(), gen_random_uuid());

-- =================================================================
-- 5. ROLE FUNCTION PERMISSIONS - Quyền của vai trò đối với chức năng
-- =================================================================

-- ADMIN: Có tất cả quyền
INSERT INTO "RoleFunctionPermissions" ("Id", "RoleId", "FunctionId", "PermissionId", "IsGranted", "CreatedAt", "UpdatedAt", "CreatedBy")
SELECT 
    gen_random_uuid(),
    r."Id",
    fp."FunctionId",
    fp."PermissionId",
    true,
    NOW(),
    NOW(),
    gen_random_uuid()
FROM "PartnerRoles" r
CROSS JOIN "FunctionPermissions" fp
WHERE r."Code" = 'ADMIN';

-- PREMIUM_PARTNER: Có hầu hết quyền trừ ADMIN_MANAGE
INSERT INTO "RoleFunctionPermissions" ("Id", "RoleId", "FunctionId", "PermissionId", "IsGranted", "CreatedAt", "UpdatedAt", "CreatedBy")
SELECT 
    gen_random_uuid(),
    r."Id",
    fp."FunctionId",
    fp."PermissionId",
    true,
    NOW(),
    NOW(),
    gen_random_uuid()
FROM "PartnerRoles" r
CROSS JOIN "FunctionPermissions" fp
INNER JOIN "Functions" f ON fp."FunctionId" = f."Id"
WHERE r."Code" = 'PREMIUM_PARTNER' 
AND f."FunctionCode" != 'ADMIN_MANAGE';

-- STANDARD_PARTNER: Có quyền cơ bản với hóa đơn và xem báo cáo
INSERT INTO "RoleFunctionPermissions" ("Id", "RoleId", "FunctionId", "PermissionId", "IsGranted", "CreatedAt", "UpdatedAt", "CreatedBy")
SELECT 
    gen_random_uuid(),
    r."Id",
    fp."FunctionId",
    fp."PermissionId",
    true,
    NOW(),
    NOW(),
    gen_random_uuid()
FROM "PartnerRoles" r
CROSS JOIN "FunctionPermissions" fp
INNER JOIN "Functions" f ON fp."FunctionId" = f."Id"
WHERE r."Code" = 'STANDARD_PARTNER' 
AND f."FunctionCode" IN ('INVOICE_CREATE', 'INVOICE_READ', 'INVOICE_UPDATE', 'INVOICE_SIGN', 'INVOICE_SEND_CQT', 'MOBIFONE_API', 'REPORT_VIEW');

-- BASIC_PARTNER: Chỉ có quyền cơ bản nhất
INSERT INTO "RoleFunctionPermissions" ("Id", "RoleId", "FunctionId", "PermissionId", "IsGranted", "CreatedAt", "UpdatedAt", "CreatedBy")
SELECT 
    gen_random_uuid(),
    r."Id",
    fp."FunctionId",
    fp."PermissionId",
    true,
    NOW(),
    NOW(),
    gen_random_uuid()
FROM "PartnerRoles" r
CROSS JOIN "FunctionPermissions" fp
INNER JOIN "Functions" f ON fp."FunctionId" = f."Id"
WHERE r."Code" = 'BASIC_PARTNER' 
AND f."FunctionCode" IN ('INVOICE_CREATE', 'INVOICE_READ', 'MOBIFONE_API');

-- TRIAL_PARTNER: Chỉ có quyền xem
INSERT INTO "RoleFunctionPermissions" ("Id", "RoleId", "FunctionId", "PermissionId", "IsGranted", "CreatedAt", "UpdatedAt", "CreatedBy")
SELECT 
    gen_random_uuid(),
    r."Id",
    fp."FunctionId",
    fp."PermissionId",
    true,
    NOW(),
    NOW(),
    gen_random_uuid()
FROM "PartnerRoles" r
CROSS JOIN "FunctionPermissions" fp
INNER JOIN "Functions" f ON fp."FunctionId" = f."Id"
INNER JOIN "Permissions" p ON fp."PermissionId" = p."Id"
WHERE r."Code" = 'TRIAL_PARTNER' 
AND f."FunctionCode" IN ('INVOICE_READ', 'REPORT_VIEW')
AND p."PermissionCode" = 'READ';

-- =================================================================
-- 6. PARTNERS - Các đối tác mẫu
-- =================================================================
INSERT INTO "Partners" ("Id", "PartnerCode", "PartnerName", "CompanyName", "ContactEmail", "ContactPhone", "ClientId", "ClientSecret", "HmacSecretKey", "IsActive", "IpWhitelist", "Metadata", "CreatedAt", "UpdatedAt", "CreatedBy") VALUES
(gen_random_uuid(), 'ADMIN001', 'Administrator', 'ZenInvoice Company', '<EMAIL>', '+84901234567', 'admin_client_001', '$2a$11$12345HASHEDPASSWORD123456789ABCDEF', 'HMAC_SECRET_KEY_FOR_ADMIN_001_VERY_LONG_AND_SECURE', true, '["127.0.0.1", "***********/24", "10.0.0.0/8"]', '{"department": "IT", "level": "admin"}', NOW(), NOW(), gen_random_uuid()),

(gen_random_uuid(), 'MOBIFONE001', 'MobiFone Corporation', 'MobiFone Corporation', '<EMAIL>', '+84912345678', 'mobifone_client_001', '$2a$11$MOBIFONE_HASHED_PASSWORD_123456789ABCDEF', 'HMAC_SECRET_KEY_FOR_MOBIFONE_001_VERY_LONG_AND_SECURE', true, '["***********/24", "*************/24"]', '{"company": "mobifone", "tier": "premium"}', NOW(), NOW(), gen_random_uuid()),

(gen_random_uuid(), 'TESTPARTNER001', 'Test Partner', 'Test Company Ltd', '<EMAIL>', '+84923456789', 'test_client_001', '$2a$11$TEST_HASHED_PASSWORD_123456789ABCDEF', 'HMAC_SECRET_KEY_FOR_TEST_001_VERY_LONG_AND_SECURE', true, '["*************", "10.0.0.50"]', '{"environment": "test", "tier": "standard"}', NOW(), NOW(), gen_random_uuid()),

(gen_random_uuid(), 'TRIAL001', 'Trial User', 'Trial Company', '<EMAIL>', '+84934567890', 'trial_client_001', '$2a$11$TRIAL_HASHED_PASSWORD_123456789ABCDEF', 'HMAC_SECRET_KEY_FOR_TRIAL_001_VERY_LONG_AND_SECURE', true, '["0.0.0.0/0"]', '{"environment": "trial", "expiresAt": "2024-12-31T23:59:59Z"}', NOW(), NOW(), gen_random_uuid());

-- =================================================================
-- 7. PARTNER ROLE ASSIGNMENTS - Gán vai trò cho đối tác
-- =================================================================
INSERT INTO "PartnerRoleAssignments" ("Id", "PartnerId", "RoleId", "AssignedAt", "ExpiresAt", "IsActive", "AssignedBy", "Reason", "CreatedAt", "UpdatedAt", "CreatedBy")
SELECT 
    gen_random_uuid(),
    p."Id",
    r."Id",
    NOW(),
    CASE 
        WHEN p."PartnerCode" = 'TRIAL001' THEN NOW() + INTERVAL '30 days'
        ELSE NULL 
    END,
    true,
    gen_random_uuid(),
    'Initial role assignment',
    NOW(),
    NOW(),
    gen_random_uuid()
FROM "Partners" p
INNER JOIN "PartnerRoles" r ON 
    (p."PartnerCode" = 'ADMIN001' AND r."Code" = 'ADMIN') OR
    (p."PartnerCode" = 'MOBIFONE001' AND r."Code" = 'PREMIUM_PARTNER') OR
    (p."PartnerCode" = 'TESTPARTNER001' AND r."Code" = 'STANDARD_PARTNER') OR
    (p."PartnerCode" = 'TRIAL001' AND r."Code" = 'TRIAL_PARTNER');

-- =================================================================
-- 8. PARTNER CONSTRAINTS - Ràng buộc cho đối tác
-- =================================================================
INSERT INTO "PartnerConstraints" ("Id", "PartnerId", "ConstraintType", "ConstraintValue", "ValidFrom", "ValidTo", "IsActive", "SetBy", "Description", "Priority", "CreatedAt", "UpdatedAt", "CreatedBy")
SELECT 
    gen_random_uuid(),
    p."Id",
    'MONTHLY_INVOICE_LIMIT',
    CASE 
        WHEN p."PartnerCode" = 'ADMIN001' THEN '1000000'
        WHEN p."PartnerCode" = 'MOBIFONE001' THEN '500000'
        WHEN p."PartnerCode" = 'TESTPARTNER001' THEN '100000'
        WHEN p."PartnerCode" = 'TRIAL001' THEN '1000'
    END,
    NOW(),
    CASE 
        WHEN p."PartnerCode" = 'TRIAL001' THEN NOW() + INTERVAL '30 days'
        ELSE NULL 
    END,
    true,
    gen_random_uuid(),
    'Giới hạn số lượng hóa đơn tối đa mỗi tháng',
    100,
    NOW(),
    NOW(),
    gen_random_uuid()
FROM "Partners" p;

INSERT INTO "PartnerConstraints" ("Id", "PartnerId", "ConstraintType", "ConstraintValue", "ValidFrom", "ValidTo", "IsActive", "SetBy", "Description", "Priority", "CreatedAt", "UpdatedAt", "CreatedBy")
SELECT 
    gen_random_uuid(),
    p."Id",
    'API_RATE_LIMIT_PER_HOUR',
    CASE 
        WHEN p."PartnerCode" = 'ADMIN001' THEN '10000'
        WHEN p."PartnerCode" = 'MOBIFONE001' THEN '5000'
        WHEN p."PartnerCode" = 'TESTPARTNER001' THEN '2000'
        WHEN p."PartnerCode" = 'TRIAL001' THEN '100'
    END,
    NOW(),
    CASE 
        WHEN p."PartnerCode" = 'TRIAL001' THEN NOW() + INTERVAL '30 days'
        ELSE NULL 
    END,
    true,
    gen_random_uuid(),
    'Giới hạn số lượng API call tối đa mỗi giờ',
    100,
    NOW(),
    NOW(),
    gen_random_uuid()
FROM "Partners" p;

-- =================================================================
-- 9. Sample AUTHENTICATION LOGS - Log xác thực mẫu
-- =================================================================
INSERT INTO "AuthenticationLogs" ("Id", "PartnerId", "AuthenticationType", "IpAddress", "UserAgent", "Success", "ErrorMessage", "TokenId", "Metadata", "CreatedAt", "UpdatedAt", "CreatedBy")
SELECT 
    gen_random_uuid(),
    p."Id",
    'OAuth2_ClientCredentials',
    '*************',
    'ZenInvoice-Client/1.0',
    true,
    NULL,
    gen_random_uuid(),
    '{"loginTime": "2024-01-01T10:00:00Z", "sessionDuration": 7200}',
    NOW() - INTERVAL '1 hour',
    NOW() - INTERVAL '1 hour',
    p."Id"
FROM "Partners" p
WHERE p."PartnerCode" IN ('ADMIN001', 'MOBIFONE001', 'TESTPARTNER001');

-- =================================================================
-- Verify inserted data
-- =================================================================
SELECT 'Data insertion completed successfully!' as status;

-- Check counts
SELECT 
    (SELECT COUNT(*) FROM "Functions") as functions_count,
    (SELECT COUNT(*) FROM "Permissions") as permissions_count,
    (SELECT COUNT(*) FROM "FunctionPermissions") as function_permissions_count,
    (SELECT COUNT(*) FROM "PartnerRoles") as roles_count,
    (SELECT COUNT(*) FROM "RoleFunctionPermissions") as role_permissions_count,
    (SELECT COUNT(*) FROM "Partners") as partners_count,
    (SELECT COUNT(*) FROM "PartnerRoleAssignments") as role_assignments_count,
    (SELECT COUNT(*) FROM "PartnerConstraints") as constraints_count,
    (SELECT COUNT(*) FROM "AuthenticationLogs") as auth_logs_count;