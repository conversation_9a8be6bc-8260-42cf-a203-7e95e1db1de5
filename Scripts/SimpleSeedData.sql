-- =================================================================
-- ZenInvoice Simple Seed Data for Testing
-- =================================================================
-- Script dữ liệu đơn giản để test authentication/authorization

-- Xóa dữ liệu cũ (nếu có)
DELETE FROM "RoleFunctionPermissions";
DELETE FROM "PartnerRoleAssignments";
DELETE FROM "PartnerFunctionPermissions";
DELETE FROM "PartnerConstraints";
DELETE FROM "AuthenticationLogs";
DELETE FROM "PartnerUsages";
DELETE FROM "PartnerTokens";
DELETE FROM "FunctionPermissions";
DELETE FROM "Partners";
DELETE FROM "PartnerRoles";
DELETE FROM "Functions";
DELETE FROM "Permissions";

-- =================================================================
-- 1. PERMISSIONS - Các quyền hạn cơ bản
-- =================================================================
INSERT INTO "Permissions" ("Id", "Code", "Name", "Description", "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted") VALUES
('22222222-2222-2222-2222-222222222221', 'CREATE', 'Tạo mới', 'Quyền tạo mới dữ liệu', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('22222222-2222-2222-2222-222222222222', 'READ', 'Đọc', 'Quyền đọc/xem dữ liệu', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('22222222-2222-2222-2222-222222222225', 'EXECUTE', 'Thực thi', 'Quyền thực thi chức năng', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 2. FUNCTIONS - Các chức năng cơ bản
-- =================================================================
INSERT INTO "Functions" ("Id", "Code", "Name", "Description", "Module", "IsActive", "DisplayOrder", "Metadata", "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted") VALUES
('11111111-1111-1111-1111-111111111111', 'INVOICE_CREATE', 'Tạo hóa đơn', 'Chức năng tạo hóa đơn điện tử', 'INVOICE', true, 1, '{}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111112', 'INVOICE_READ', 'Xem hóa đơn', 'Chức năng xem thông tin hóa đơn', 'INVOICE', true, 2, '{}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111117', 'MOBIFONE_API', 'MobiFone API', 'Chức năng gọi API MobiFone', 'INTEGRATION', true, 10, '{}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111120', 'ADMIN_MANAGE', 'Quản trị hệ thống', 'Chức năng quản trị toàn hệ thống', 'ADMIN', true, 100, '{}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 3. FUNCTION PERMISSIONS - Mapping Functions và Permissions
-- =================================================================
-- INVOICE_CREATE có quyền CREATE và EXECUTE
INSERT INTO "FunctionPermissions" ("Id", "FunctionId", "PermissionId", "IsRequired", "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted") VALUES
('33333333-3333-3333-3333-333333333301', '11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222221', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333302', '11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222225', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- INVOICE_READ có quyền READ
INSERT INTO "FunctionPermissions" ("Id", "FunctionId", "PermissionId", "IsRequired", "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted") VALUES
('33333333-3333-3333-3333-333333333303', '11111111-1111-1111-1111-111111111112', '22222222-2222-2222-2222-222222222222', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- MOBIFONE_API có quyền EXECUTE
INSERT INTO "FunctionPermissions" ("Id", "FunctionId", "PermissionId", "IsRequired", "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted") VALUES
('33333333-3333-3333-3333-333333333312', '11111111-1111-1111-1111-111111111117', '22222222-2222-2222-2222-222222222225', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- ADMIN_MANAGE có tất cả quyền
INSERT INTO "FunctionPermissions" ("Id", "FunctionId", "PermissionId", "IsRequired", "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted") VALUES
('33333333-3333-3333-3333-333333333315', '11111111-1111-1111-1111-111111111120', '22222222-2222-2222-2222-222222222221', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333316', '11111111-1111-1111-1111-111111111120', '22222222-2222-2222-2222-222222222222', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333319', '11111111-1111-1111-1111-111111111120', '22222222-2222-2222-2222-222222222225', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 4. PARTNER ROLES - Vai trò đối tác
-- =================================================================
INSERT INTO "PartnerRoles" ("Id", "Code", "Name", "Description", "Priority", "IsActive", "DefaultApiRateLimitPerHour", "DefaultMonthlyInvoiceLimit", "Metadata", "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted") VALUES
('*************-4444-4444-************', 'ADMIN', 'Admin', 'Quản trị viên', 1000, true, 10000, 1000000, '{}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('*************-4444-4444-************', 'USER', 'User', 'Người dùng thường', 500, true, 1000, 50000, '{}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 5. ROLE FUNCTION PERMISSIONS - Quyền của vai trò
-- =================================================================
-- ADMIN có tất cả quyền
INSERT INTO "RoleFunctionPermissions" ("Id", "RoleId", "FunctionId", "PermissionId", "IsGranted", "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted")
SELECT 
    gen_random_uuid(),
    '*************-4444-4444-************',
    fp."FunctionId",
    fp."PermissionId",
    true,
    NOW(),
    NOW(),
    '00000000-0000-0000-0000-000000000001',
    false
FROM "FunctionPermissions" fp;

-- USER chỉ có quyền cơ bản
INSERT INTO "RoleFunctionPermissions" ("Id", "RoleId", "FunctionId", "PermissionId", "IsGranted", "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted")
SELECT 
    gen_random_uuid(),
    '*************-4444-4444-************',
    fp."FunctionId",
    fp."PermissionId",
    true,
    NOW(),
    NOW(),
    '00000000-0000-0000-0000-000000000001',
    false
FROM "FunctionPermissions" fp
INNER JOIN "Functions" f ON fp."FunctionId" = f."Id"
WHERE f."Code" IN ('INVOICE_READ', 'MOBIFONE_API');

-- =================================================================
-- 6. PARTNERS - Đối tác test
-- =================================================================
INSERT INTO "Partners" ("Id", "ClientId", "ClientSecretHash", "HmacSecretHash", "Name", "ContactEmail", "ContactPhone", "Description", "IpWhitelist", "EnableIpWhitelist", "IsActive", "ApiRateLimitPerHour", "MonthlyInvoiceLimit", "CurrentMonthUsage", "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted") VALUES
-- Test Admin (password: admin123)
('*************-5555-5555-************', 'test_admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test Administrator', '<EMAIL>', '+84901234567', 'Tài khoản admin test', '[]', false, true, 10000, 1000000, 0, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- Test User (password: user123)
('*************-5555-5555-555555555552', 'test_user', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test User', '<EMAIL>', '+84901234568', 'Tài khoản user test', '[]', false, true, 1000, 50000, 0, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 7. PARTNER ROLE ASSIGNMENTS - Gán vai trò
-- =================================================================
INSERT INTO "PartnerRoleAssignments" ("Id", "PartnerId", "RoleId", "AssignedAt", "ExpiresAt", "IsActive", "AssignedBy", "Reason", "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted") VALUES
('66666666-6666-6666-6666-666666666661', '*************-5555-5555-************', '*************-4444-4444-************', NOW(), NULL, true, '00000000-0000-0000-0000-000000000001', 'Test admin role', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('66666666-6666-6666-6666-666666666662', '*************-5555-5555-555555555552', '*************-4444-4444-************', NOW(), NULL, true, '00000000-0000-0000-0000-000000000001', 'Test user role', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- Verify data
-- =================================================================
SELECT 'Simple seed data completed!' as status;

SELECT 
    (SELECT COUNT(*) FROM "Partners") as partners_count,
    (SELECT COUNT(*) FROM "PartnerRoles") as roles_count,
    (SELECT COUNT(*) FROM "Functions") as functions_count,
    (SELECT COUNT(*) FROM "Permissions") as permissions_count;

-- Display partner credentials
SELECT 
    p."Name" as partner_name,
    p."ClientId" as client_id,
    'Password is hashed, use raw text for testing' as password_note,
    pr."Name" as role_name
FROM "Partners" p
LEFT JOIN "PartnerRoleAssignments" pra ON p."Id" = pra."PartnerId" AND pra."IsActive" = true
LEFT JOIN "PartnerRoles" pr ON pra."RoleId" = pr."Id"
ORDER BY p."Name";