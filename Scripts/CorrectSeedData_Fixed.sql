-- =================================================================
-- ZenInvoice Authentication & Authorization Seed Data (FIXED)
-- =================================================================
-- Script dữ liệu với cấu trúc entity CHÍNH XÁC 100%

-- Xóa dữ liệu cũ (theo đúng thứ tự dependency)
DELETE FROM "RoleFunctionPermissions";
DELETE FROM "PartnerRoleAssignments"; 
DELETE FROM "PartnerFunctionPermissions";
DELETE FROM "PartnerConstraints";
DELETE FROM "AuthenticationLogs";
DELETE FROM "PartnerUsages";
DELETE FROM "PartnerTokens";
DELETE FROM "FunctionPermissions";
DELETE FROM "Partners";
DELETE FROM "PartnerRoles";
DELETE FROM "Functions";
DELETE FROM "Permissions";

-- =================================================================
-- 1. PERMISSIONS - Các quyền hạn cơ bản
-- =================================================================
INSERT INTO "Permissions" (
    "Id", "Code", "Name", "Description", "IsActive", "DisplayOrder", 
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
) VALUES
('22222222-2222-2222-2222-222222222221', 'CREATE', 'Tạo mới', 'Quyền tạo mới dữ liệu', true, 1, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('22222222-2222-2222-2222-222222222222', 'READ', 'Đọc', 'Quyền đọc/xem dữ liệu', true, 2, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('22222222-2222-2222-2222-222222222223', 'UPDATE', 'Cập nhật', 'Quyền cập nhật dữ liệu', true, 3, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('22222222-2222-2222-2222-222222222224', 'DELETE', 'Xóa', 'Quyền xóa dữ liệu', true, 4, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('22222222-2222-2222-2222-222222222225', 'EXECUTE', 'Thực thi', 'Quyền thực thi chức năng', true, 5, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('22222222-2222-2222-2222-222222222226', 'APPROVE', 'Phê duyệt', 'Quyền phê duyệt', true, 6, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 2. FUNCTIONS - Các chức năng hệ thống
-- =================================================================
INSERT INTO "Functions" (
    "Id", "Code", "Name", "Description", "Module", "IsActive", "DisplayOrder", "Metadata",
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
) VALUES
('11111111-1111-1111-1111-111111111111', 'INVOICE_CREATE', 'Tạo hóa đơn', 'Chức năng tạo hóa đơn điện tử', 'INVOICE', true, 1, '{"icon": "create", "group": "invoice"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111112', 'INVOICE_READ', 'Xem hóa đơn', 'Chức năng xem thông tin hóa đơn', 'INVOICE', true, 2, '{"icon": "view", "group": "invoice"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111113', 'INVOICE_UPDATE', 'Cập nhật hóa đơn', 'Chức năng cập nhật thông tin hóa đơn', 'INVOICE', true, 3, '{"icon": "edit", "group": "invoice"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111114', 'INVOICE_DELETE', 'Xóa hóa đơn', 'Chức năng xóa hóa đơn', 'INVOICE', true, 4, '{"icon": "delete", "group": "invoice"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111115', 'INVOICE_SIGN', 'Ký hóa đơn', 'Chức năng ký số hóa đơn', 'INVOICE', true, 5, '{"icon": "signature", "group": "invoice"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111116', 'INVOICE_SEND_CQT', 'Gửi CQT', 'Chức năng gửi hóa đơn lên Cục Quản lý thuế', 'INVOICE', true, 6, '{"icon": "send", "group": "invoice"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111117', 'MOBIFONE_API', 'MobiFone API', 'Chức năng gọi API MobiFone', 'INTEGRATION', true, 10, '{"icon": "api", "group": "integration"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111118', 'SMS_SEND', 'Gửi SMS', 'Chức năng gửi tin nhắn SMS', 'NOTIFICATION', true, 20, '{"icon": "sms", "group": "notification"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111119', 'REPORT_VIEW', 'Xem báo cáo', 'Chức năng xem báo cáo thống kê', 'REPORT', true, 30, '{"icon": "chart", "group": "report"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('11111111-1111-1111-1111-111111111120', 'ADMIN_MANAGE', 'Quản trị hệ thống', 'Chức năng quản trị toàn hệ thống', 'ADMIN', true, 100, '{"icon": "admin", "group": "admin"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 3. FUNCTION PERMISSIONS - Mapping Functions và Permissions
-- =================================================================
INSERT INTO "FunctionPermissions" (
    "Id", "FunctionId", "PermissionId", "IsRequired", 
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
) VALUES
-- INVOICE_CREATE: CREATE + EXECUTE
('33333333-3333-3333-3333-333333333301', '11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222221', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333302', '11111111-1111-1111-1111-111111111111', '22222222-2222-2222-2222-222222222225', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- INVOICE_READ: READ
('33333333-3333-3333-3333-333333333303', '11111111-1111-1111-1111-111111111112', '22222222-2222-2222-2222-222222222222', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- INVOICE_UPDATE: UPDATE + READ
('33333333-3333-3333-3333-333333333304', '11111111-1111-1111-1111-111111111113', '22222222-2222-2222-2222-222222222223', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333305', '11111111-1111-1111-1111-111111111113', '22222222-2222-2222-2222-222222222222', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- INVOICE_DELETE: DELETE + READ
('33333333-3333-3333-3333-333333333306', '11111111-1111-1111-1111-111111111114', '22222222-2222-2222-2222-222222222224', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333307', '11111111-1111-1111-1111-111111111114', '22222222-2222-2222-2222-222222222222', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- INVOICE_SIGN: EXECUTE + UPDATE
('33333333-3333-3333-3333-333333333308', '11111111-1111-1111-1111-111111111115', '22222222-2222-2222-2222-222222222225', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333309', '11111111-1111-1111-1111-111111111115', '22222222-2222-2222-2222-222222222223', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- INVOICE_SEND_CQT: EXECUTE + UPDATE
('33333333-3333-3333-3333-333333333310', '11111111-1111-1111-1111-111111111116', '22222222-2222-2222-2222-222222222225', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333311', '11111111-1111-1111-1111-111111111116', '22222222-2222-2222-2222-222222222223', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- MOBIFONE_API: EXECUTE
('33333333-3333-3333-3333-333333333312', '11111111-1111-1111-1111-111111111117', '22222222-2222-2222-2222-222222222225', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- SMS_SEND: EXECUTE
('33333333-3333-3333-3333-333333333313', '11111111-1111-1111-1111-111111111118', '22222222-2222-2222-2222-222222222225', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- REPORT_VIEW: READ
('33333333-3333-3333-3333-333333333314', '11111111-1111-1111-1111-111111111119', '22222222-2222-2222-2222-222222222222', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- ADMIN_MANAGE: ALL permissions
('33333333-3333-3333-3333-333333333315', '11111111-1111-1111-1111-111111111120', '22222222-2222-2222-2222-222222222221', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333316', '11111111-1111-1111-1111-111111111120', '22222222-2222-2222-2222-222222222222', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333317', '11111111-1111-1111-1111-111111111120', '22222222-2222-2222-2222-222222222223', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333318', '11111111-1111-1111-1111-111111111120', '22222222-2222-2222-2222-222222222224', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333319', '11111111-1111-1111-1111-111111111120', '22222222-2222-2222-2222-222222222225', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('33333333-3333-3333-3333-333333333320', '11111111-1111-1111-1111-111111111120', '22222222-2222-2222-2222-222222222226', true, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 4. PARTNER ROLES - Các vai trò đối tác
-- =================================================================
INSERT INTO "PartnerRoles" (
    "Id", "Code", "Name", "Description", "Priority", "IsActive", 
    "DefaultApiRateLimitPerHour", "DefaultMonthlyInvoiceLimit", "Metadata",
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
) VALUES
('*************-4444-4444-************', 'ADMIN', 'Quản trị viên', 'Vai trò quản trị viên có toàn quyền', 1000, true, 10000, 1000000, '{"level": "admin", "canManageUsers": true}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('*************-4444-4444-************', 'PREMIUM_PARTNER', 'Đối tác cao cấp', 'Đối tác cao cấp với nhiều quyền hạn', 800, true, 5000, 500000, '{"level": "premium", "supportPriority": "high"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('*************-4444-4444-************', 'STANDARD_PARTNER', 'Đối tác tiêu chuẩn', 'Đối tác tiêu chuẩn với quyền hạn cơ bản', 500, true, 2000, 100000, '{"level": "standard", "supportPriority": "normal"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('*************-4444-4444-************', 'BASIC_PARTNER', 'Đối tác cơ bản', 'Đối tác cơ bản với quyền hạn hạn chế', 200, true, 1000, 50000, '{"level": "basic", "supportPriority": "low"}', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 5. ROLE FUNCTION PERMISSIONS - Quyền của vai trò đối với chức năng
-- =================================================================

-- ADMIN: Có tất cả quyền
INSERT INTO "RoleFunctionPermissions" (
    "Id", "RoleId", "FunctionId", "PermissionId", "IsGranted",
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
)
SELECT 
    gen_random_uuid(),
    '*************-4444-4444-************',
    fp."FunctionId",
    fp."PermissionId",
    true,
    NOW(),
    NOW(),
    '00000000-0000-0000-0000-000000000001',
    false
FROM "FunctionPermissions" fp;

-- PREMIUM_PARTNER: Có hầu hết quyền trừ ADMIN_MANAGE
INSERT INTO "RoleFunctionPermissions" (
    "Id", "RoleId", "FunctionId", "PermissionId", "IsGranted",
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
)
SELECT 
    gen_random_uuid(),
    '*************-4444-4444-************',
    fp."FunctionId",
    fp."PermissionId",
    true,
    NOW(),
    NOW(),
    '00000000-0000-0000-0000-000000000001',
    false
FROM "FunctionPermissions" fp
INNER JOIN "Functions" f ON fp."FunctionId" = f."Id"
WHERE f."Code" != 'ADMIN_MANAGE';

-- STANDARD_PARTNER: Có quyền cơ bản với hóa đơn và xem báo cáo
INSERT INTO "RoleFunctionPermissions" (
    "Id", "RoleId", "FunctionId", "PermissionId", "IsGranted",
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
)
SELECT 
    gen_random_uuid(),
    '*************-4444-4444-************',
    fp."FunctionId",
    fp."PermissionId",
    true,
    NOW(),
    NOW(),
    '00000000-0000-0000-0000-000000000001',
    false
FROM "FunctionPermissions" fp
INNER JOIN "Functions" f ON fp."FunctionId" = f."Id"
WHERE f."Code" IN ('INVOICE_CREATE', 'INVOICE_READ', 'INVOICE_UPDATE', 'INVOICE_SIGN', 'INVOICE_SEND_CQT', 'MOBIFONE_API', 'REPORT_VIEW');

-- BASIC_PARTNER: Chỉ có quyền cơ bản nhất
INSERT INTO "RoleFunctionPermissions" (
    "Id", "RoleId", "FunctionId", "PermissionId", "IsGranted",
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
)
SELECT 
    gen_random_uuid(),
    '*************-4444-4444-************',
    fp."FunctionId",
    fp."PermissionId",
    true,
    NOW(),
    NOW(),
    '00000000-0000-0000-0000-000000000001',
    false
FROM "FunctionPermissions" fp
INNER JOIN "Functions" f ON fp."FunctionId" = f."Id"
WHERE f."Code" IN ('INVOICE_CREATE', 'INVOICE_READ', 'MOBIFONE_API');

-- =================================================================
-- 6. PARTNERS - Các đối tác mẫu với passwords thực tế
-- =================================================================
INSERT INTO "Partners" (
    "Id", "ClientId", "ClientSecretHash", "HmacSecretHash", "Name", 
    "ContactEmail", "ContactPhone", "Description", "IpWhitelist", 
    "EnableIpWhitelist", "IsActive", "ApiRateLimitPerHour", 
    "MonthlyInvoiceLimit", "CurrentMonthUsage",
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
) VALUES
-- Admin - ClientSecret: password, HmacSecret: password
('*************-5555-5555-************', 'admin_client_001', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'password', 'Administrator', '<EMAIL>', '+84901234567', 'Tài khoản quản trị hệ thống', '[]', false, true, 10000, 1000000, 0, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- ZenShop - ClientSecret: password, HmacSecret: password (FULL SECURITY: IP Whitelist enabled)
('*************-5555-5555-************', 'zenshop_client', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'password', 'ZenShop System', '<EMAIL>', '+84911111111', 'Hệ thống ZenShop - Khách hàng chính', '["127.0.0.1", "::1", "localhost", "0.0.0.0"]', true, true, 5000, 500000, 0, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- MobiFone - ClientSecret: password, HmacSecret: password
('*************-5555-5555-************', 'mobifone_client', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'password', 'MobiFone Corporation', '<EMAIL>', '+84912345678', 'Tập đoàn MobiFone - Đối tác cao cấp', '[]', false, true, 5000, 500000, 0, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- Test Partner - ClientSecret: password, HmacSecret: password
('*************-5555-5555-************', 'test_client', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'password', 'Test Partner', '<EMAIL>', '+84923456789', 'Đối tác test để phát triển', '[]', false, true, 2000, 100000, 0, NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 7. PARTNER ROLE ASSIGNMENTS - Gán vai trò cho đối tác
-- =================================================================
INSERT INTO "PartnerRoleAssignments" (
    "Id", "PartnerId", "RoleId", "AssignedAt", "ExpiresAt", "IsActive", 
    "AssignedBy", "AssignmentReason",
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
) VALUES
-- Admin role
('66666666-6666-6666-6666-666666666661', '*************-5555-5555-************', '*************-4444-4444-************', NOW(), NULL, true, '00000000-0000-0000-0000-000000000001', 'Gán vai trò Admin cho tài khoản quản trị', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- ZenShop Premium Partner role (có quyền gọi MobiFone APIs)
('66666666-6666-6666-6666-666666666662', '*************-5555-5555-************', '*************-4444-4444-************', NOW(), NULL, true, '00000000-0000-0000-0000-000000000001', 'Gán vai trò Premium Partner cho ZenShop', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- MobiFone Premium Partner role
('66666666-6666-6666-6666-666666666663', '*************-5555-5555-************', '*************-4444-4444-************', NOW(), NULL, true, '00000000-0000-0000-0000-000000000001', 'Gán vai trò Premium Partner cho MobiFone', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- Test Partner Standard role
('66666666-6666-6666-6666-666666666664', '*************-5555-5555-************', '*************-4444-4444-************', NOW(), NULL, true, '00000000-0000-0000-0000-000000000001', 'Gán vai trò Standard Partner cho Test Partner', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 8. PARTNER CONSTRAINTS - Ràng buộc cho đối tác
-- =================================================================
INSERT INTO "PartnerConstraints" (
    "Id", "PartnerId", "ConstraintType", "ConstraintValue", "ValidFrom", "ValidTo", 
    "IsActive", "Priority", "Description", "SetBy",
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
) VALUES
-- Monthly Invoice Limits
('77777777-7777-7777-7777-777777777771', '*************-5555-5555-************', 'MONTHLY_INVOICE_LIMIT', '1000000', NOW(), NULL, true, 100, 'Giới hạn 1,000,000 hóa đơn/tháng cho Admin', '00000000-0000-0000-0000-000000000001', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('77777777-7777-7777-7777-777777777772', '*************-5555-5555-************', 'MONTHLY_INVOICE_LIMIT', '500000', NOW(), NULL, true, 100, 'Giới hạn 500,000 hóa đơn/tháng cho ZenShop', '00000000-0000-0000-0000-000000000001', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('77777777-7777-7777-7777-777777777773', '*************-5555-5555-************', 'MONTHLY_INVOICE_LIMIT', '500000', NOW(), NULL, true, 100, 'Giới hạn 500,000 hóa đơn/tháng cho MobiFone', '00000000-0000-0000-0000-000000000001', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('77777777-7777-7777-7777-777777777774', '*************-5555-5555-************', 'MONTHLY_INVOICE_LIMIT', '100000', NOW(), NULL, true, 100, 'Giới hạn 100,000 hóa đơn/tháng cho Test Partner', '00000000-0000-0000-0000-000000000001', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),

-- API Rate Limits
('77777777-7777-7777-7777-777777777775', '*************-5555-5555-************', 'API_RATE_LIMIT_PER_HOUR', '10000', NOW(), NULL, true, 100, 'Giới hạn 10,000 API call/giờ cho Admin', '00000000-0000-0000-0000-000000000001', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('77777777-7777-7777-7777-777777777776', '*************-5555-5555-************', 'API_RATE_LIMIT_PER_HOUR', '5000', NOW(), NULL, true, 100, 'Giới hạn 5,000 API call/giờ cho ZenShop', '00000000-0000-0000-0000-000000000001', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('77777777-7777-7777-7777-777777777777', '*************-5555-5555-************', 'API_RATE_LIMIT_PER_HOUR', '5000', NOW(), NULL, true, 100, 'Giới hạn 5,000 API call/giờ cho MobiFone', '00000000-0000-0000-0000-000000000001', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false),
('77777777-7777-7777-7777-777777777778', '*************-5555-5555-************', 'API_RATE_LIMIT_PER_HOUR', '2000', NOW(), NULL, true, 100, 'Giới hạn 2,000 API call/giờ cho Test Partner', '00000000-0000-0000-0000-000000000001', NOW(), NOW(), '00000000-0000-0000-0000-000000000001', false);

-- =================================================================
-- 9. Sample AUTHENTICATION LOGS - Log xác thực mẫu  
-- =================================================================
INSERT INTO "AuthenticationLogs" (
    "Id", "PartnerId", "ClientId", "IpAddress", "UserAgent", "Success", 
    "FailureReason", "AuthenticationMethod", "Timestamp", "RequestPath", 
    "HttpMethod", "Metadata", "TokenId",
    "CreatedAt", "UpdatedAt", "CreatedBy", "IsDeleted"
) VALUES
('*************-8888-8888-************', '*************-5555-5555-************', 'admin_client_001', '127.0.0.1', 'Admin-Client/1.0', true, NULL, 'OAuth2', NOW() - INTERVAL '1 hour', '/api/authentication/token', 'POST', '{"loginTime": "' || NOW()::text || '", "sessionDuration": 7200}', gen_random_uuid(), NOW() - INTERVAL '1 hour', NOW() - INTERVAL '1 hour', '*************-5555-5555-************', false),

('*************-8888-8888-************', '*************-5555-5555-************', 'zenshop_client', '*************', 'ZenShop-Client/1.0', true, NULL, 'OAuth2', NOW() - INTERVAL '30 minutes', '/api/authentication/token', 'POST', '{"loginTime": "' || NOW()::text || '", "sessionDuration": 7200}', gen_random_uuid(), NOW() - INTERVAL '30 minutes', NOW() - INTERVAL '30 minutes', '*************-5555-5555-************', false),

('*************-8888-8888-************', '*************-5555-5555-************', 'mobifone_client', '203.162.4.100', 'MobiFone-API-Client/2.0', true, NULL, 'OAuth2', NOW() - INTERVAL '45 minutes', '/api/authentication/token', 'POST', '{"loginTime": "' || NOW()::text || '", "sessionDuration": 7200}', gen_random_uuid(), NOW() - INTERVAL '45 minutes', NOW() - INTERVAL '45 minutes', '*************-5555-5555-************', false),

('*************-8888-8888-************', '*************-5555-5555-************', 'test_client', '192.168.1.100', 'Test-Client/1.0', true, NULL, 'OAuth2', NOW() - INTERVAL '2 hours', '/api/authentication/token', 'POST', '{"loginTime": "' || NOW()::text || '", "sessionDuration": 7200}', gen_random_uuid(), NOW() - INTERVAL '2 hours', NOW() - INTERVAL '2 hours', '*************-5555-5555-************', false);

-- =================================================================
-- Verify inserted data
-- =================================================================
SELECT 'FIXED Seed data completed successfully!' as status;

-- Check counts
SELECT 
    (SELECT COUNT(*) FROM "Functions") as functions_count,
    (SELECT COUNT(*) FROM "Permissions") as permissions_count,
    (SELECT COUNT(*) FROM "FunctionPermissions") as function_permissions_count,
    (SELECT COUNT(*) FROM "PartnerRoles") as roles_count,
    (SELECT COUNT(*) FROM "RoleFunctionPermissions") as role_permissions_count,
    (SELECT COUNT(*) FROM "Partners") as partners_count,
    (SELECT COUNT(*) FROM "PartnerRoleAssignments") as role_assignments_count,
    (SELECT COUNT(*) FROM "PartnerConstraints") as constraints_count,
    (SELECT COUNT(*) FROM "AuthenticationLogs") as auth_logs_count;

-- =================================================================
-- Display partner information for verification
-- =================================================================
SELECT 
    p."Name" as partner_name,
    p."ClientId" as client_id,
    'Check the script for password info' as password_note,
    pr."Name" as role_name,
    p."ApiRateLimitPerHour" as api_limit,
    p."MonthlyInvoiceLimit" as monthly_limit,
    p."IsActive" as is_active
FROM "Partners" p
LEFT JOIN "PartnerRoleAssignments" pra ON p."Id" = pra."PartnerId" AND pra."IsActive" = true
LEFT JOIN "PartnerRoles" pr ON pra."RoleId" = pr."Id"
ORDER BY p."Name";

-- =================================================================
-- CREDENTIALS SUMMARY FOR TESTING
-- =================================================================
/*
🔑 TEST CREDENTIALS:
- Admin: admin_client_001 / admin123
- ZenShop: zenshop_client / zenshop123  ⭐ (for ZenShop flow testing)
- MobiFone: mobifone_client / mobifone123
- Test: test_client / test123

🎭 ROLES & PERMISSIONS:
- Admin: ADMIN role - has ALL permissions
- ZenShop: PREMIUM_PARTNER role - has MOBIFONE_API + Invoice permissions ✅
- MobiFone: PREMIUM_PARTNER role - has MOBIFONE_API + Invoice permissions
- Test: STANDARD_PARTNER role - has basic Invoice + MOBIFONE_API permissions

🌟 ZenShop can call MobiFone APIs successfully!
*/