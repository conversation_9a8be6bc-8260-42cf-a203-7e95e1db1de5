# ZenInvoice Authentication & Authorization Usage Guide

## Tổng quan hệ thống

ZenInvoice đã được tích hợp hệ thống authentication/authorization đa lớp bảo mật bao gồm:

- **OAuth2 Client Credentials** cho x<PERSON>c thực đối tác
- **HMAC-SHA256** cho xác thực tính toàn vẹn request
- **IP Whitelist** cho bảo mật b<PERSON> sung
- **Dynamic Permission System** với database-driven authorization
- **Business Constraint Validation** (rate limits, monthly limits)
- **Comprehensive Audit Logging** và usage tracking

## 1. Cài đặt dữ liệu

### Bước 1: Chạy Migration
```bash
dotnet ef database update --project Infrastructure --startup-project API
```

### Bước 2: Insert dữ liệu mẫu
```bash
# Kết nối PostgreSQL và chạy script
psql -h localhost -U your_username -d your_database -f Scripts/SeedData.sql
```

Hoặc copy nội dung file `Scripts/SeedData.sql` và chạy trong database client.

## 2. Dữ liệu mẫu đã được tạo

### Partners (Đối tác)
1. **ADMIN001** - Administrator (Role: ADMIN)
   - ClientId: `admin_client_001`
   - IP Whitelist: `["127.0.0.1", "***********/24", "10.0.0.0/8"]`

2. **MOBIFONE001** - MobiFone Corporation (Role: PREMIUM_PARTNER)
   - ClientId: `mobifone_client_001`
   - IP Whitelist: `["***********/24", "*************/24"]`

3. **TESTPARTNER001** - Test Partner (Role: STANDARD_PARTNER)
   - ClientId: `test_client_001`
   - IP Whitelist: `["*************", "*********"]`

4. **TRIAL001** - Trial User (Role: TRIAL_PARTNER)
   - ClientId: `trial_client_001`
   - IP Whitelist: `["0.0.0.0/0"]` (Allow all)

### Functions & Permissions
- **INVOICE_CREATE** - Tạo hóa đơn (CREATE, EXECUTE)
- **INVOICE_READ** - Xem hóa đơn (READ)
- **INVOICE_UPDATE** - Cập nhật hóa đơn (UPDATE, READ)
- **INVOICE_DELETE** - Xóa hóa đơn (DELETE, READ)
- **INVOICE_SIGN** - Ký hóa đơn (EXECUTE, UPDATE)
- **INVOICE_SEND_CQT** - Gửi CQT (EXECUTE, UPDATE)
- **MOBIFONE_API** - MobiFone API (EXECUTE)
- **SMS_SEND** - Gửi SMS (EXECUTE)
- **REPORT_VIEW** - Xem báo cáo (READ)
- **ADMIN_MANAGE** - Quản trị hệ thống (ALL)

## 3. API Authentication

### 3.1 Lấy Access Token
```http
POST /api/authentication/token
Content-Type: application/json

{
  "clientId": "admin_client_001",
  "clientSecret": "your_client_secret",
  "grantType": "client_credentials"
}
```

Response:
```json
{
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 7200,
    "scope": "invoice:create invoice:read"
  },
  "code": "000",
  "message": "Token issued successfully"
}
```

### 3.2 Sử dụng Access Token
```http
GET /api/mobifone-invoice/data-references
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
X-Timestamp: 1703123456
X-Signature: calculated_hmac_signature
```

## 4. HMAC Signature

### 4.1 Cách tính HMAC Signature
```
StringToSign = HTTP_METHOD + "\n" + REQUEST_PATH + "\n" + TIMESTAMP + "\n" + CLIENT_ID + "\n" + PAYLOAD
Signature = HMAC-SHA256(StringToSign, HMAC_SECRET_KEY)
```

### 4.2 Ví dụ tính toán
```csharp
string httpMethod = "POST";
string requestPath = "/api/mobifone-invoice/login";
string timestamp = "1703123456";
string clientId = "admin_client_001";
string payload = "{\"username\":\"test\"}";
string hmacSecret = "HMAC_SECRET_KEY_FOR_ADMIN_001_VERY_LONG_AND_SECURE";

string stringToSign = $"{httpMethod}\n{requestPath}\n{timestamp}\n{clientId}\n{payload}";
string signature = GenerateHMAC(stringToSign, hmacSecret);
```

### 4.3 Headers cần thiết
```http
X-Timestamp: 1703123456
X-Signature: calculated_hmac_signature
X-Client-Id: admin_client_001
```

## 5. Authorization trong Controller

### 5.1 Sử dụng RequirePermission Attribute
```csharp
[HttpPost("create-invoice")]
[RequirePermission("INVOICE_CREATE", "CREATE")]
public async Task<IActionResult> CreateInvoice([FromBody] CreateInvoiceRequest request)
{
    // Implementation
}
```

### 5.2 Sử dụng RequireRole Attribute
```csharp
[HttpGet("admin/users")]
[RequireRole("ADMIN")]
public async Task<IActionResult> GetAllUsers()
{
    // Implementation
}
```

### 5.3 Multiple Permissions
```csharp
[HttpPut("invoice/{id}")]
[RequirePermission("INVOICE_UPDATE", "UPDATE")]
[RequirePermission("INVOICE_READ", "READ")]
public async Task<IActionResult> UpdateInvoice(Guid id, [FromBody] UpdateInvoiceRequest request)
{
    // Implementation
}
```

## 6. Middleware Pipeline

Thứ tự middleware pipeline đã được cấu hình:
1. **IpWhitelistMiddleware** - Kiểm tra IP whitelist
2. **JwtAuthenticationMiddleware** - Xác thực JWT token
3. **SignatureValidationMiddleware** - Xác thực HMAC signature
4. **PermissionAuthorizationMiddleware** - Kiểm tra quyền hạn
5. **UsageTrackingMiddleware** - Theo dõi sử dụng API

## 7. Admin APIs

### 7.1 Quản lý quyền hạn đối tác
```http
# Xem quyền hạn của đối tác
GET /api/admin/partners/{partnerId}/permissions
Authorization: Bearer {admin_token}

# Cấp quyền cho đối tác
POST /api/admin/partners/{partnerId}/permissions
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "functionCode": "INVOICE_CREATE",
  "permissionCode": "CREATE",
  "reason": "Cấp quyền tạo hóa đơn",
  "expiresAt": "2024-12-31T23:59:59Z"
}
```

### 7.2 Quản lý vai trò
```http
# Gán vai trò cho đối tác
POST /api/admin/partners/{partnerId}/roles
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "roleCode": "PREMIUM_PARTNER",
  "reason": "Nâng cấp lên đối tác cao cấp",
  "expiresAt": null
}
```

### 7.3 Quản lý ràng buộc
```http
# Thiết lập giới hạn cho đối tác
POST /api/admin/partners/{partnerId}/constraints
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "constraintType": "MONTHLY_INVOICE_LIMIT",
  "constraintValue": "50000",
  "validFrom": "2024-01-01T00:00:00Z",
  "validTo": null,
  "description": "Giới hạn 50,000 hóa đơn/tháng",
  "priority": 100
}
```

### 7.4 Thống kê sử dụng
```http
# Xem thống kê sử dụng của đối tác
GET /api/admin/partners/{partnerId}/usage?period=2024-01
Authorization: Bearer {admin_token}
```

## 8. Error Codes

| Code | Description |
|------|-------------|
| 000 | Success |
| 400 | Bad Request |
| 401 | Unauthorized (Authentication failed) |
| 403 | Forbidden (Authorization failed) |
| 404 | Not Found |
| 429 | Too Many Requests (Rate limited) |
| 500 | Internal Server Error |

## 9. Constraint Types

| Constraint Type | Description |
|-----------------|-------------|
| API_RATE_LIMIT_PER_HOUR | Giới hạn số API call/giờ |
| MONTHLY_INVOICE_LIMIT | Giới hạn số hóa đơn/tháng |
| DAILY_REQUEST_LIMIT | Giới hạn số request/ngày |
| IP_WHITELIST | Danh sách IP được phép |

## 10. Role Hierarchy

| Role | Priority | Default API Limit | Default Monthly Limit |
|------|----------|-------------------|----------------------|
| ADMIN | 1000 | 10,000/hour | 1,000,000/month |
| PREMIUM_PARTNER | 800 | 5,000/hour | 500,000/month |
| STANDARD_PARTNER | 500 | 2,000/hour | 100,000/month |
| BASIC_PARTNER | 200 | 1,000/hour | 50,000/month |
| TRIAL_PARTNER | 100 | 100/hour | 1,000/month |

## 11. Testing

### 11.1 Test Authentication
```bash
curl -X POST "http://localhost:5000/api/authentication/token" \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "admin_client_001",
    "clientSecret": "your_secret",
    "grantType": "client_credentials"
  }'
```

### 11.2 Test API với Authorization
```bash
curl -X GET "http://localhost:5000/api/mobifone-invoice/data-references" \
  -H "Authorization: Bearer your_access_token" \
  -H "X-Timestamp: $(date +%s)" \
  -H "X-Signature: calculated_signature" \
  -H "X-Client-Id: admin_client_001"
```

## 12. Monitoring & Logging

Hệ thống tự động log các hoạt động:
- Authentication attempts
- Authorization checks
- API usage statistics
- Error events
- Performance metrics

Logs được lưu trong:
- Database: `AuthenticationLogs`, `PartnerUsage` tables
- File: `logs/ZenInvoice.log`
- Console: Development environment

## 13. Security Best Practices

1. **Luôn sử dụng HTTPS** trong production
2. **Bảo mật Client Secret** - không hard-code trong source code
3. **Rotate HMAC keys** định kỳ
4. **Monitor suspicious activities** qua logs
5. **Set proper IP whitelists** cho từng đối tác
6. **Review permissions** định kỳ
7. **Use strong passwords** cho database connections
8. **Keep secrets in secure vaults** (Azure Key Vault, HashiCorp Vault, etc.)

## 14. Troubleshooting

### 14.1 Authentication Issues
- Check client credentials
- Verify IP whitelist
- Check token expiration
- Review authentication logs

### 14.2 Authorization Issues
- Verify partner permissions
- Check role assignments
- Review function-permission mappings
- Check constraint violations

### 14.3 Signature Issues
- Verify HMAC secret key
- Check timestamp validity (±5 minutes)
- Ensure correct string-to-sign format
- Verify Base64 encoding

## 15. Production Deployment

### 15.1 Environment Variables
```bash
# Database
ConnectionStrings__DefaultConnection="Server=prod-server;Database=zeninvoice;..."

# JWT Settings
JwtSettings__SecretKey="your-super-secret-key-for-production"
JwtSettings__Issuer="ZenInvoice"
JwtSettings__Audience="ZenInvoice-API"
JwtSettings__ExpiryInHours="2"

# Logging
Serilog__MinimumLevel="Information"
```

### 15.2 Security Headers
Thêm các security headers trong production:
```csharp
app.UseSecurityHeaders();
app.UseHsts();
app.UseHttpsRedirection();
```

### 15.3 Rate Limiting
Cấu hình rate limiting global:
```csharp
app.UseRateLimiter();
```

---

**Lưu ý**: Đây là hệ thống authentication/authorization hoàn chỉnh với tất cả các layer bảo mật. Trong môi trường development, một số validation có thể được relaxed để dễ dàng testing.