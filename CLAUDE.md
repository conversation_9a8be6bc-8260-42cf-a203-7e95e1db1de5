# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ZenInvoice is a Vietnamese electronic invoice management system built with .NET 8, featuring extensive integration with MobiFone's invoice API. The system follows Clean Architecture principles with CQRS pattern using MediatR.

## Architecture

### Clean Architecture Layers
- **API**: ASP.NET Core Web API controllers and middleware
- **Applications**: CQRS commands/queries, handlers, DTOs, and business logic
- **Infrastructure**: Entity Framework, repositories, external service integrations
- **Core**: Domain entities and interfaces
- **Shared**: Common utilities, exceptions, and response patterns

### Key Patterns
- **CQRS**: Commands and Queries separated using MediatR
- **Repository Pattern**: Base repository with Unit of Work
- **Primary Constructor Pattern**: Modern C# 12 syntax throughout
- **Response Wrapper**: Consistent `Response<T>` pattern for all API responses

## Development Commands

### Build and Run
```bash
# Build the entire solution
dotnet build

# Run the API (will start at https://localhost:7241)
dotnet run --project API

# Run in development mode
dotnet run --project API --launch-profile https
```

### Database
```bash
# Add new migration
dotnet ef migrations add MigrationName --project Infrastructure --startup-project API

# Update database
dotnet ef database update --project Infrastructure --startup-project API

# Drop database (development only)
dotnet ef database drop --project Infrastructure --startup-project API
```

### Testing
```bash
# Run all tests (if any exist)
dotnet test

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"
```

## MobiFone Integration

### API Patterns
- All MobiFone APIs require `X-Token` and `X-MaDvcs` headers
- Raw DTOs match MobiFone's exact API specification
- Thread-safe HttpClient with per-request headers (NOT DefaultRequestHeaders)
- Comprehensive logging with curl command generation

### Controller Structure
- Base route: `zenInvoice/api/[controller]`
- Vietnamese endpoint names matching MobiFone documentation
- Extensive XML documentation for all endpoints

### Service Pattern
```csharp
// HttpClient injection pattern (thread-safe)
builder.Services.AddHttpClient<IMobiFoneInvoiceService, MobiFoneInvoiceService>(client =>
{
    client.Timeout = TimeSpan.FromSeconds(30);
    client.DefaultRequestHeaders.Add("User-Agent", "ZenInvoice/1.0");
});
```

## CQRS Implementation

### Command Pattern
```csharp
public record LoginCommand(LoginRequest Request) : IRequest<Response<LoginResponse>>;

public class LoginCommandHandler(IMobiFoneInvoiceService service) : IRequestHandler<LoginCommand, Response<LoginResponse>>
{
    public async Task<Response<LoginResponse>> Handle(LoginCommand request, CancellationToken cancellationToken)
    {
        return await service.LoginAsync(request.Request, cancellationToken);
    }
}
```

### Validation
- FluentValidation integrated with MediatR pipeline
- ValidationBehavior automatically handles validation errors
- Returns Response<T> with error details

## Key Configuration

### appsettings.json Structure
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "..."
  },
  "MobiFoneInvoice": {
    "IsTest": true,
    "TestUrl": "...",
    "ProductionUrl": "..."
  },
  "MBFSMSConfig": {
    "BaseUrl": "...",
    "Username": "...",
    "Password": "..."
  }
}
```

### Launch Profiles
- `http`: Development (localhost:5119)
- `https`: Localhost environment (localhost:7241)
- `Docker`: Containerized deployment

## Important Notes

### Thread Safety
- **NEVER** use `HttpClient.DefaultRequestHeaders` in services
- Use per-request headers: `request.Headers.Add("key", "value")`
- MobiFoneInvoiceService demonstrates correct thread-safe pattern

### Response Handling
- All APIs return `Response<T>` with consistent structure
- Success: `Code = "000"`
- Error codes defined in `ErrorCodes` constants
- TraceId included for debugging

### Logging
- Serilog configured for file and console output
- Curl commands logged for API debugging
- Structured logging with correlation IDs

### Vietnamese Invoice Types
The system supports multiple Vietnamese invoice types:
- GTGT (Giá trị gia tăng) - Value Added Tax invoices
- Bán hàng - Sales invoices
- PXKVCNB - Internal transport warehouse export slips
- PXKDL - Agent consignment warehouse export slips
- Various other government-required invoice formats

## Common File Locations

- Controllers: `API/Controllers/`
- CQRS Commands: `Applications/Features/*/Commands/`
- CQRS Queries: `Applications/Features/*/Queries/`
- DTOs: `Applications/DTOs/MobiFoneInvoice/`
- Services: `Infrastructure/Services/`
- Entities: `Core/Entities/`
- Migrations: `Infrastructure/Migrations/`