using Applications.DTOs.Partners;
using Applications.Features.Partners.Commands;
using AutoMapper;
using Core.Entities.Authentication;

namespace Applications.MappingProfiles;

/// <summary>
/// AutoMapper profile for Partner entity mappings
/// </summary>
public class PartnerProfile : Profile
{
    public PartnerProfile()
    {
        // Partner entity to PartnerResponse DTO
        CreateMap<Partner, PartnerResponse>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.ClientId, opt => opt.MapFrom(src => src.ClientId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.ContactEmail, opt => opt.MapFrom(src => src.ContactEmail))
            .ForMember(dest => dest.ContactPhone, opt => opt.MapFrom(src => src.ContactPhone))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.IpWhitelist, opt => opt.MapFrom(src => src.IpWhitelist))
            .ForMember(dest => dest.EnableIpWhitelist, opt => opt.MapFrom(src => src.EnableIpWhitelist))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive))
            .ForMember(dest => dest.ApiRateLimitPerHour, opt => opt.MapFrom(src => src.ApiRateLimitPerHour))
            .ForMember(dest => dest.MonthlyInvoiceLimit, opt => opt.MapFrom(src => src.MonthlyInvoiceLimit))
            .ForMember(dest => dest.CurrentMonthUsage, opt => opt.MapFrom(src => src.CurrentMonthUsage))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => src.UpdatedAt))
            .ForMember(dest => dest.CreatedBy, opt => opt.MapFrom(src => src.CreatedBy));

        // CreatePartnerRequest to CreatePartnerCommand
        CreateMap<CreatePartnerRequest, CreatePartnerCommand>()
            .ForMember(dest => dest.ClientId, opt => opt.MapFrom(src => src.ClientId))
            .ForMember(dest => dest.ClientSecret, opt => opt.MapFrom(src => src.ClientSecret))
            .ForMember(dest => dest.HmacSecret, opt => opt.MapFrom(src => src.HmacSecret))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.ContactEmail, opt => opt.MapFrom(src => src.ContactEmail))
            .ForMember(dest => dest.ContactPhone, opt => opt.MapFrom(src => src.ContactPhone))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.IpWhitelist, opt => opt.MapFrom(src => src.IpWhitelist))
            .ForMember(dest => dest.EnableIpWhitelist, opt => opt.MapFrom(src => src.EnableIpWhitelist))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.IsActive))
            .ForMember(dest => dest.ApiRateLimitPerHour, opt => opt.MapFrom(src => src.ApiRateLimitPerHour))
            .ForMember(dest => dest.MonthlyInvoiceLimit, opt => opt.MapFrom(src => src.MonthlyInvoiceLimit))
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore()); // Will be set in controller
    }
}
