using System.Text.Json.Serialization;
using Applications.DTOs.MobiFoneInvoice.Common;

namespace Applications.DTOs.MobiFoneInvoice.GetInvoiceFromdateTodate.Raws;

/// <summary>
/// Response DTO cho API 4.16 L<PERSON>y danh sách hóa đơn theo khoảng thời gian
/// URL: {{base_url}}/api/Invoice68/GetInvoiceFromdateTodate
/// Method: POST
/// ContentType: application/json
/// Authorization: Bear Token;ma_dvcs
/// Đây là DTO raw theo chuẩn tài liệu MobiFone, giữ nguyên tên field
/// Response trả về mảng các đối tượng hóa đơn
/// </summary>
public class GetInvoiceFromdateTodateResponse : List<InvoiceListItem>
{
    // Response này kế thừa từ List<InvoiceListItem> vì API trả về mảng trực tiếp
    // Không có wrapper object bên ngoài
}

/// <summary>
/// Error Response DTO cho API 4.16 khi có lỗi
/// </summary>
public class GetInvoiceFromdateTodateErrorResponse
{
    /// <summary>
    /// Thông báo lỗi
    /// Kiểu: String
    /// Mô tả: Thông báo lỗi khi yêu cầu không hợp lệ
    /// Ví dụ: "Không có dữ liệu hóa đơn!", "Dữ liệu tu_ngay/den_ngay không hợp lệ!"
    /// </summary>
    [JsonPropertyName("error")]
    public string? error { get; set; }
}
