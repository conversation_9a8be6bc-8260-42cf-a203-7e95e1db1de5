using Applications.DTOs.MobiFoneInvoice.ExportInvoiceXmlPretreatment.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để xuất XML hóa đơn trước khi ký số bằng usb token qua Plugin trong MobiFone Invoice API
/// API 4.22: ExportInvoiceXmlPretreatment
/// </summary>
public record ExportInvoiceXmlPretreatmentCommand(
    string Id,
    string Token,
    string MaDvcs) : IRequest<Response<ExportInvoiceXmlPretreatmentResponse>>;

/// <summary>
/// Validator cho ExportInvoiceXmlPretreatmentCommand
/// </summary>
public class ExportInvoiceXmlPretreatmentCommandValidator : AbstractValidator<ExportInvoiceXmlPretreatmentCommand>
{
    public ExportInvoiceXmlPretreatmentCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là GUID hợp lệ");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không được để trống");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị không được để trống");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho ExportInvoiceXmlPretreatmentCommand
/// </summary>
public class ExportInvoiceXmlPretreatmentCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<ExportInvoiceXmlPretreatmentCommand, Response<ExportInvoiceXmlPretreatmentResponse>>
{
    public async Task<Response<ExportInvoiceXmlPretreatmentResponse>> Handle(ExportInvoiceXmlPretreatmentCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.ExportInvoiceXmlPretreatmentAsync(
            request.Id,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
