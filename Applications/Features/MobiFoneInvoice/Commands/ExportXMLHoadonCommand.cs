using Applications.DTOs.MobiFoneInvoice.ExportXMLHoadon.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để lấy thông tin XML hóa đơn trong MobiFone Invoice API
/// API 4.15: ExportXMLHoadon
/// </summary>
public record ExportXMLHoadonCommand(
    string Id,
    string Token,
    string MaDvcs) : IRequest<Response<ExportXMLHoadonResponse>>;

/// <summary>
/// Validator cho ExportXMLHoadonCommand
/// </summary>
public class ExportXMLHoadonCommandValidator : AbstractValidator<ExportXMLHoadonCommand>
{
    public ExportXMLHoadonCommandValidator()
    {
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithMessage("ID hóa đơn không được để trống")
            .Must(BeValidGuid)
            .WithMessage("ID hóa đơn phải là GUID hợp lệ");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không được để trống");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị không được để trống");
    }

    private static bool BeValidGuid(string id)
    {
        return Guid.TryParse(id, out _);
    }
}

/// <summary>
/// Handler cho ExportXMLHoadonCommand
/// </summary>
public class ExportXMLHoadonCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<ExportXMLHoadonCommand, Response<ExportXMLHoadonResponse>>
{
    public async Task<Response<ExportXMLHoadonResponse>> Handle(ExportXMLHoadonCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.ExportXMLHoadonAsync(
            request.Id,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
