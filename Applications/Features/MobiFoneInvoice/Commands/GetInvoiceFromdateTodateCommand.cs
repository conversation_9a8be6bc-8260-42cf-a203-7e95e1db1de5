using Applications.DTOs.MobiFoneInvoice.GetInvoiceFromdateTodate.Raws;
using Applications.Interfaces.Services;
using FluentValidation;
using MediatR;
using Shared.Responses;

namespace Applications.Features.MobiFoneInvoice.Commands;

/// <summary>
/// Command để lấy danh sách hóa đơn theo khoảng thời gian trong MobiFone Invoice API
/// API 4.16: GetInvoiceFromdateTodate
/// </summary>
public record GetInvoiceFromdateTodateCommand(
    GetInvoiceFromdateTodateRequest Request,
    string Token,
    string MaDvcs) : IRequest<Response<GetInvoiceFromdateTodateResponse>>;

/// <summary>
/// Validator cho GetInvoiceFromdateTodateCommand
/// </summary>
public class GetInvoiceFromdateTodateCommandValidator : AbstractValidator<GetInvoiceFromdateTodateCommand>
{
    public GetInvoiceFromdateTodateCommandValidator()
    {
        RuleFor(x => x.Request)
            .NotNull()
            .WithMessage("Request không được để trống");

        RuleFor(x => x.Request.tu_ngay)
            .NotEmpty()
            .WithMessage("Ngày bắt đầu không được để trống")
            .Must(BeValidDate)
            .WithMessage("Ngày bắt đầu phải có định dạng YYYY-MM-DD");

        RuleFor(x => x.Request.den_ngay)
            .NotEmpty()
            .WithMessage("Ngày kết thúc không được để trống")
            .Must(BeValidDate)
            .WithMessage("Ngày kết thúc phải có định dạng YYYY-MM-DD");

        RuleFor(x => x)
            .Must(x => BeValidDateRange(x.Request.tu_ngay, x.Request.den_ngay))
            .WithMessage("Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc");

        RuleFor(x => x.Token)
            .NotEmpty()
            .WithMessage("Token không được để trống");

        RuleFor(x => x.MaDvcs)
            .NotEmpty()
            .WithMessage("Mã đơn vị không được để trống");
    }

    private static bool BeValidDate(string dateString)
    {
        return DateTime.TryParseExact(dateString, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out _);
    }

    private static bool BeValidDateRange(string fromDate, string toDate)
    {
        if (!BeValidDate(fromDate) || !BeValidDate(toDate))
            return false;

        var from = DateTime.ParseExact(fromDate, "yyyy-MM-dd", null);
        var to = DateTime.ParseExact(toDate, "yyyy-MM-dd", null);

        return from <= to;
    }
}

/// <summary>
/// Handler cho GetInvoiceFromdateTodateCommand
/// </summary>
public class GetInvoiceFromdateTodateCommandHandler(IMobiFoneInvoiceService mobiFoneInvoiceService)
    : IRequestHandler<GetInvoiceFromdateTodateCommand, Response<GetInvoiceFromdateTodateResponse>>
{
    public async Task<Response<GetInvoiceFromdateTodateResponse>> Handle(GetInvoiceFromdateTodateCommand request, CancellationToken cancellationToken)
    {
        return await mobiFoneInvoiceService.GetInvoiceFromdateTodateAsync(
            request.Request,
            request.Token,
            request.MaDvcs,
            cancellationToken);
    }
}
