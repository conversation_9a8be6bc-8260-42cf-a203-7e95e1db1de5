# API 4.22: <PERSON><PERSON><PERSON> XML hóa đơn trước khi ký số bằng usb token qua Plugin

## M<PERSON> tả
API này cho phép người dùng xuất XML hóa đơn trước khi ký bằng Plugin trong hệ thống MobiFone Invoice.

**Lưu ý quan trọng**: API này sử dụng Raw DTOs theo đúng chuẩn tài liệu MobiFone, tất cả tên field giữ nguyên như trong tài liệu (ví dụ: `hdon_id`, `InvoiceXmlData`, `InvoiceXmlBangKe`, `shdon`, `dvky`).

## Endpoint
```
GET /api/mobifone-invoice/export-invoice-xml-pretreatment?id={hdon_id}
```

## Headers
- `X-Token`: Token từ API login
- `X-MaDvcs`: Mã đơn vị từ API login
- `Content-Type`: application/json

## Query Parameters

### <PERSON><PERSON><PERSON> tham số bắt buộc

| <PERSON>ê<PERSON> trườ<PERSON> | <PERSON><PERSON><PERSON> dữ liệu | Đ<PERSON> dài | Bắt buộ<PERSON> | Mô tả | Ghi chú/Giá trị |
|------------|--------------|--------|----------|-------|-----------------|
| `id` | Guid | 36 ký tự | X | Id của hóa đơn | Ví dụ: fa0281b2-2b9d-448f-a213-dccce06fe13f |

## Response

### Thành công (200 OK)

Khi yêu cầu thành công, API sẽ trả về cấu trúc JSON chứa các thông tin XML của hóa đơn và các chi tiết liên quan:

```json
{
  "code": "000",
  "message": "",
  "data": {
    "hdon_id": "8d8e04a5-8089-474d-9c7b-403a33cb8897",
    "InvoiceXmlData": "<HDon></HDon>",
    "InvoiceXmlBangKe": "<BKe></BKe>",
    "shdon": "12",
    "dvky": "CÔNG TY DEMO"
  },
  "isSuccess": true
}
```

#### Cấu trúc Response Data

| Tên trường | Kiểu dữ liệu | Mô tả | Ví dụ |
|------------|--------------|-------|-------|
| `hdon_id` | String | ID của hóa đơn | "8d8e04a5-8089-474d-9c7b-403a33cb8897" |
| `InvoiceXmlData` | String | Dữ liệu XML của hóa đơn | "<HDon></HDon>" (ví dụ ngắn gọn, thực tế sẽ chứa cấu trúc XML đầy đủ) |
| `InvoiceXmlBangKe` | String | Dữ liệu XML của bảng kê kèm theo hóa đơn (nếu có) | "<BKe></BKe>" |
| `shdon` | String | Số hóa đơn | "12" |
| `dvky` | String | Đơn vị ký | "CÔNG TY DEMO" |

### Lỗi (400 Bad Request hoặc 500 Internal Server Error)

API có thể trả về các thông báo lỗi sau trong trường hợp không thành công:

```json
{
  "code": "400",
  "message": "Không tìm thấy hóa đơn",
  "data": null,
  "isSuccess": false
}
```

```json
{
  "code": "400", 
  "message": "Bạn chưa xử lý hóa đơn của ngày hôm trước",
  "data": null,
  "isSuccess": false
}
```

#### Các loại lỗi phổ biến

| Thông báo lỗi | Nguyên nhân | Giải pháp |
|---------------|-------------|-----------|
| "Không tìm thấy hóa đơn" | ID hóa đơn không chính xác hoặc không tồn tại | Kiểm tra lại ID hóa đơn |
| "Bạn chưa xử lý hóa đơn của ngày hôm trước" | Có vấn đề liên quan đến quy trình xử lý hóa đơn theo ngày | Xử lý hóa đơn của ngày trước đó trước khi thực hiện |

## Ví dụ sử dụng

### Request
```http
GET /api/mobifone-invoice/export-invoice-xml-pretreatment?id=fa0281b2-2b9d-448f-a213-dccce06fe13f
X-Token: QkdMNHJhVGthUjRJdm9MelpjZXp4cmFOV285cjNLb0xSUnBhdTRzaisvND06QURNSU5
X-MaDvcs: VP
Content-Type: application/json
```

### Response thành công
```json
{
  "code": "000",
  "message": "",
  "data": {
    "hdon_id": "fa0281b2-2b9d-448f-a213-dccce06fe13f",
    "InvoiceXmlData": "<HDon><DLHDon Id=\"data\">...</DLHDon><DLQRCode>...</DLQRCode><DSCKS>...</DSCKS></HDon>",
    "InvoiceXmlBangKe": "<BKe></BKe>",
    "shdon": "12",
    "dvky": "CÔNG TY DEMO"
  },
  "isSuccess": true
}
```

## Lưu ý kỹ thuật

1. **Validation**: ID hóa đơn phải là GUID hợp lệ
2. **Authentication**: Cần có Token và MaDvcs hợp lệ từ API login
3. **Method**: Sử dụng GET method với query parameter
4. **Response Format**: Trả về Raw DTO theo chuẩn MobiFone, giữ nguyên tên field
5. **Error Handling**: API có thể trả về lỗi nghiệp vụ cụ thể từ hệ thống MobiFone

## So sánh với API 4.15

API 4.22 (ExportInvoiceXmlPretreatment) và API 4.15 (ExportXMLHoadon) có cấu trúc response tương tự nhưng khác nhau về mục đích sử dụng:

- **API 4.15**: Lấy thông tin XML hóa đơn (thông thường)
- **API 4.22**: Xuất XML hóa đơn trước khi ký số bằng USB token qua Plugin (chuyên biệt cho quy trình ký số)
