using Applications.DTOs.Partners;
using MediatR;
using Shared.Responses;

namespace Applications.Features.Partners.Commands;

/// <summary>
/// Command to create a new partner
/// </summary>
public class CreatePartnerCommand : IRequest<Response<PartnerResponse>>
{
    /// <summary>
    /// Client ID for OAuth2 authentication (public identifier)
    /// </summary>
    public string ClientId { get; set; } = null!;

    /// <summary>
    /// Client secret for OAuth2 authentication (will be hashed)
    /// </summary>
    public string ClientSecret { get; set; } = null!;

    /// <summary>
    /// HMAC secret for payload signature validation
    /// </summary>
    public string HmacSecret { get; set; } = null!;

    /// <summary>
    /// Partner name/company name
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Contact email for this partner
    /// </summary>
    public string? ContactEmail { get; set; }

    /// <summary>
    /// Contact phone for this partner
    /// </summary>
    public string? ContactPhone { get; set; }

    /// <summary>
    /// Description/notes about this partner
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// JSON array of whitelisted IP addresses/CIDR ranges
    /// </summary>
    public string? IpWhitelist { get; set; }

    /// <summary>
    /// Whether IP whitelist checking is enabled for this partner
    /// </summary>
    public bool EnableIpWhitelist { get; set; } = true;

    /// <summary>
    /// Whether this partner is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// API rate limit per hour for this partner
    /// </summary>
    public int ApiRateLimitPerHour { get; set; } = 1000;

    /// <summary>
    /// Monthly invoice purchase limit for this partner
    /// </summary>
    public decimal MonthlyInvoiceLimit { get; set; } = 10000;

    /// <summary>
    /// User ID creating this partner
    /// </summary>
    public Guid CreatedBy { get; set; }
}
