using Applications.DTOs.Partners;
using Applications.Features.Partners.Commands;
using Applications.Interfaces.Repositories;
using AutoMapper;
using Core.Entities.Authentication;
using MediatR;
using Microsoft.Extensions.Logging;
using Shared.Constants;
using Shared.Responses;

namespace Applications.Features.Partners.Handlers;

/// <summary>
/// Handler for creating a new partner
/// </summary>
public class CreatePartnerHandler : IRequestHandler<CreatePartnerCommand, Response<PartnerResponse>>
{
    private readonly IPartnerRepository _partnerRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<CreatePartnerHandler> _logger;

    public CreatePartnerHandler(
        IPartnerRepository partnerRepository,
        IMapper mapper,
        ILogger<CreatePartnerHandler> logger)
    {
        _partnerRepository = partnerRepository;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<Response<PartnerResponse>> Handle(CreatePartnerCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Creating new partner with ClientId: {ClientId}", request.ClientId);

        try
        {
            // 1. Check if ClientId already exists
            var clientIdExists = await _partnerRepository.ClientIdExistsAsync(request.ClientId, cancellationToken);

            if (clientIdExists)
            {
                _logger.LogWarning("Partner with ClientId {ClientId} already exists", request.ClientId);
                return Response<PartnerResponse>.Failure<PartnerResponse>(
                    "ClientId already exists", ErrorCodes.BAD_REQUEST_ERROR);
            }

            // 2. Validate IP whitelist format if provided
            if (!string.IsNullOrEmpty(request.IpWhitelist))
            {
                if (!IsValidIpWhitelistFormat(request.IpWhitelist))
                {
                    return Response<PartnerResponse>.Failure<PartnerResponse>(
                        "Invalid IP whitelist format. Expected JSON array of IP addresses/CIDR ranges",
                        ErrorCodes.BAD_REQUEST_ERROR);
                }
            }

            // 3. Create new partner entity
            var partner = new Partner
            {
                Id = Guid.NewGuid(),
                ClientId = request.ClientId,
                ClientSecretHash = BCrypt.Net.BCrypt.HashPassword(request.ClientSecret), // Hash client secret
                HmacSecretHash = request.HmacSecret, // Store HMAC secret as plaintext (TODO: encrypt)
                Name = request.Name,
                ContactEmail = request.ContactEmail,
                ContactPhone = request.ContactPhone,
                Description = request.Description,
                IpWhitelist = request.IpWhitelist ?? "[]",
                EnableIpWhitelist = request.EnableIpWhitelist,
                IsActive = request.IsActive,
                ApiRateLimitPerHour = request.ApiRateLimitPerHour,
                MonthlyInvoiceLimit = request.MonthlyInvoiceLimit,
                CurrentMonthUsage = 0,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = request.CreatedBy,
                IsDeleted = false
            };

            // 4. Save to database
            await _partnerRepository.AddAsync(partner);
            await _partnerRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Partner created successfully with ID: {PartnerId}", partner.Id);

            // 5. Map to response DTO (excludes sensitive data)
            var response = _mapper.Map<PartnerResponse>(partner);

            return Response<PartnerResponse>.Success(response, "Partner created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating partner with ClientId: {ClientId}", request.ClientId);
            return Response<PartnerResponse>.Failure<PartnerResponse>(
                "Internal server error while creating partner", ErrorCodes.INTERNAL_SERVER_ERROR);
        }
    }

    /// <summary>
    /// Validates IP whitelist JSON format
    /// </summary>
    private static bool IsValidIpWhitelistFormat(string ipWhitelist)
    {
        try
        {
            var ips = System.Text.Json.JsonSerializer.Deserialize<string[]>(ipWhitelist);
            return ips != null;
        }
        catch
        {
            return false;
        }
    }
}
