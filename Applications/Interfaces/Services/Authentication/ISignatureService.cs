using Shared.Responses;

namespace Applications.Interfaces.Services.Authentication;

/// <summary>
/// Interface for HMAC signature generation and validation
/// </summary>
public interface ISignatureService
{
    /// <summary>
    /// Generate HMAC-SHA256 signature for a request
    /// </summary>
    /// <param name="httpMethod">HTTP method (GET, POST, etc.)</param>
    /// <param name="requestPath">Request path without query string</param>
    /// <param name="timestamp">Unix timestamp</param>
    /// <param name="clientId">Client ID</param>
    /// <param name="payload">JSON payload (empty string for GET requests)</param>
    /// <param name="hmacSecret">Partner's HMAC secret</param>
    /// <returns>Generated signature</returns>
    string GenerateSignature(
        string httpMethod,
        string requestPath,
        string timestamp,
        string clientId,
        string payload,
        string hmacSecret);

    /// <summary>
    /// Validate HMAC signature for an incoming request
    /// </summary>
    /// <param name="partnerId">Partner ID for secret lookup</param>
    /// <param name="httpMethod">HTTP method</param>
    /// <param name="requestPath">Request path</param>
    /// <param name="timestamp">Timestamp from X-Timestamp header</param>
    /// <param name="clientId">Client ID from X-Client-ID header</param>
    /// <param name="payload">Request body payload</param>
    /// <param name="providedSignature">Signature from X-Signature header</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Signature validation result</returns>
    Task<Response<SignatureValidationResult>> ValidateSignatureAsync(
        Guid partnerId,
        string httpMethod,
        string requestPath,
        string timestamp,
        string clientId,
        string payload,
        string providedSignature,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate timestamp to prevent replay attacks
    /// </summary>
    /// <param name="timestamp">Unix timestamp string</param>
    /// <param name="toleranceSeconds">Allowed time difference in seconds</param>
    /// <returns>Timestamp validation result</returns>
    Response<bool> ValidateTimestamp(string timestamp, int toleranceSeconds = 300);

    /// <summary>
    /// Get current Unix timestamp
    /// </summary>
    /// <returns>Current Unix timestamp as string</returns>
    string GetCurrentTimestamp();

    /// <summary>
    /// Convert Unix timestamp to DateTime
    /// </summary>
    /// <param name="timestamp">Unix timestamp string</param>
    /// <returns>DateTime if valid, null otherwise</returns>
    DateTime? ParseTimestamp(string timestamp);
}

/// <summary>
/// Result of signature validation
/// </summary>
public class SignatureValidationResult
{
    public bool IsValid { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime RequestTime { get; set; }
    public string ExpectedSignature { get; set; } = null!;
    public string ProvidedSignature { get; set; } = null!;
}