using Core.Entities.Authentication;
using Shared.Responses;

namespace Applications.Interfaces.Services.Authentication;

/// <summary>
/// Interface for OAuth2 client credentials authentication
/// </summary>
public interface IAuthenticationService
{
    /// <summary>
    /// Authenticate partner using OAuth2 client credentials
    /// </summary>
    /// <param name="clientId">Partner's client ID</param>
    /// <param name="clientSecret">Partner's client secret</param>
    /// <param name="ipAddress">Client IP address</param>
    /// <param name="userAgent">Client user agent</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Authentication result with access token</returns>
    Task<Response<AuthenticationResult>> AuthenticateAsync(
        string clientId, 
        string clientSecret, 
        string ipAddress, 
        string? userAgent = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate an existing access token
    /// </summary>
    /// <param name="accessToken">JWT access token to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Token validation result</returns>
    Task<Response<TokenValidationResult>> ValidateTokenAsync(
        string accessToken,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Revoke a partner's access token
    /// </summary>
    /// <param name="accessToken">Token to revoke</param>
    /// <param name="reason">Reason for revocation</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Revocation result</returns>
    Task<Response<bool>> RevokeTokenAsync(
        string accessToken, 
        string reason,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if IP address is whitelisted for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="ipAddress">IP address to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>IP whitelist check result</returns>
    Task<Response<bool>> IsIpWhitelistedAsync(
        Guid partnerId, 
        string ipAddress,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Result of authentication operation
/// </summary>
public class AuthenticationResult
{
    public string AccessToken { get; set; } = null!;
    public DateTime ExpiresAt { get; set; }
    public int ExpiresIn { get; set; }
    public string TokenType { get; set; } = "Bearer";
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = null!;
    public string[] Scopes { get; set; } = Array.Empty<string>();
}

/// <summary>
/// Result of token validation
/// </summary>
public class TokenValidationResult
{
    public bool IsValid { get; set; }
    public Guid? PartnerId { get; set; }
    public string? PartnerName { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public string[] Scopes { get; set; } = Array.Empty<string>();
    public string? ErrorMessage { get; set; }
}