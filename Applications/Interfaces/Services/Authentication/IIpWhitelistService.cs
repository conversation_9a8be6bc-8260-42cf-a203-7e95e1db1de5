using Shared.Responses;

namespace Applications.Interfaces.Services.Authentication;

/// <summary>
/// Interface for IP whitelist validation
/// </summary>
public interface IIpWhitelistService
{
    /// <summary>
    /// Check if an IP address is whitelisted for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="ipAddress">IP address to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>IP whitelist validation result</returns>
    Task<Response<IpWhitelistResult>> ValidateIpAsync(
        Guid partnerId, 
        string ipAddress,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Parse and validate IP whitelist configuration
    /// </summary>
    /// <param name="ipWhitelistJson">JSON array of IP addresses/CIDR ranges</param>
    /// <returns>Validation result</returns>
    Response<string[]> ParseIpWhitelist(string? ipWhitelistJson);

    /// <summary>
    /// Check if an IP address matches any of the whitelist entries
    /// </summary>
    /// <param name="ipAddress">IP address to check</param>
    /// <param name="whitelistEntries">Array of IP addresses/CIDR ranges</param>
    /// <returns>True if IP is whitelisted</returns>
    bool IsIpInWhitelist(string ipAddress, string[] whitelistEntries);

    /// <summary>
    /// Validate IP address format
    /// </summary>
    /// <param name="ipAddress">IP address to validate</param>
    /// <returns>True if valid IPv4 or IPv6 address</returns>
    bool IsValidIpAddress(string ipAddress);

    /// <summary>
    /// Validate CIDR notation
    /// </summary>
    /// <param name="cidr">CIDR notation to validate</param>
    /// <returns>True if valid CIDR notation</returns>
    bool IsValidCidr(string cidr);

    /// <summary>
    /// Get client IP address from HTTP context, considering proxy headers
    /// </summary>
    /// <param name="httpContext">HTTP context</param>
    /// <returns>Real client IP address</returns>
    string GetClientIpAddress(Microsoft.AspNetCore.Http.HttpContext httpContext);
}

/// <summary>
/// Result of IP whitelist validation
/// </summary>
public class IpWhitelistResult
{
    public bool IsAllowed { get; set; }
    public string IpAddress { get; set; } = null!;
    public string? MatchedRule { get; set; }
    public string? ErrorMessage { get; set; }
    public bool WhitelistEnabled { get; set; }
}