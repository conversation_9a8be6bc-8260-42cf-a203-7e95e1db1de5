using Core.Entities.Authentication;
using Shared.Responses;

namespace Applications.Interfaces.Services.Authorization;

/// <summary>
/// Interface for role management operations
/// </summary>
public interface IRoleService
{
    /// <summary>
    /// Get all roles assigned to a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of partner roles</returns>
    Task<Response<IEnumerable<PartnerRoleInfo>>> GetPartnerRolesAsync(
        Guid partnerId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Assign a role to a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="roleCode">Role code to assign</param>
    /// <param name="assignedBy">Who assigned this role</param>
    /// <param name="reason">Reason for assignment</param>
    /// <param name="expiresAt">When this assignment expires (optional)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Assignment result</returns>
    Task<Response<bool>> AssignRoleAsync(
        Guid partnerId,
        string roleCode,
        Guid assignedBy,
        string reason,
        DateTime? expiresAt = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove a role from a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="roleCode">Role code to remove</param>
    /// <param name="removedBy">Who removed this role</param>
    /// <param name="reason">Reason for removal</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Removal result</returns>
    Task<Response<bool>> RemoveRoleAsync(
        Guid partnerId,
        string roleCode,
        Guid removedBy,
        string reason,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if a partner has a specific role
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="roleCode">Role code to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Role check result</returns>
    Task<Response<bool>> HasRoleAsync(
        Guid partnerId,
        string roleCode,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all available roles in the system
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of all roles</returns>
    Task<Response<IEnumerable<PartnerRole>>> GetAllRolesAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get role by code
    /// </summary>
    /// <param name="roleCode">Role code</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Role information</returns>
    Task<Response<PartnerRole?>> GetRoleByCodeAsync(
        string roleCode,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a new role
    /// </summary>
    /// <param name="roleCode">Unique role code</param>
    /// <param name="name">Role name</param>
    /// <param name="description">Role description</param>
    /// <param name="priority">Role priority</param>
    /// <param name="defaultApiRateLimit">Default API rate limit</param>
    /// <param name="defaultMonthlyInvoiceLimit">Default monthly invoice limit</param>
    /// <param name="createdBy">Who created this role</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Creation result</returns>
    Task<Response<PartnerRole>> CreateRoleAsync(
        string roleCode,
        string name,
        string description,
        int priority,
        int defaultApiRateLimit,
        decimal defaultMonthlyInvoiceLimit,
        Guid createdBy,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update role information
    /// </summary>
    /// <param name="roleId">Role ID</param>
    /// <param name="name">New role name</param>
    /// <param name="description">New role description</param>
    /// <param name="priority">New role priority</param>
    /// <param name="defaultApiRateLimit">New default API rate limit</param>
    /// <param name="defaultMonthlyInvoiceLimit">New default monthly invoice limit</param>
    /// <param name="updatedBy">Who updated this role</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Update result</returns>
    Task<Response<bool>> UpdateRoleAsync(
        Guid roleId,
        string name,
        string description,
        int priority,
        int defaultApiRateLimit,
        decimal defaultMonthlyInvoiceLimit,
        Guid updatedBy,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete a role (soft delete)
    /// </summary>
    /// <param name="roleId">Role ID</param>
    /// <param name="deletedBy">Who deleted this role</param>
    /// <param name="reason">Reason for deletion</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Deletion result</returns>
    Task<Response<bool>> DeleteRoleAsync(
        Guid roleId,
        Guid deletedBy,
        string reason,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Clear cached roles for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    void ClearRoleCache(Guid partnerId);
}

/// <summary>
/// Information about a partner's role assignment
/// </summary>
public class PartnerRoleInfo
{
    public Guid RoleId { get; set; }
    public string RoleCode { get; set; } = null!;
    public string RoleName { get; set; } = null!;
    public string? Description { get; set; }
    public int Priority { get; set; }
    public DateTime AssignedAt { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public bool IsActive { get; set; }
    public string? AssignmentReason { get; set; }
    public bool IsValid => IsActive && (ExpiresAt == null || ExpiresAt > DateTime.UtcNow);
}