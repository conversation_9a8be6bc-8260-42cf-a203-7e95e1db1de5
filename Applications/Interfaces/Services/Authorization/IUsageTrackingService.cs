using Shared.Responses;

namespace Applications.Interfaces.Services.Authorization;

/// <summary>
/// Interface for tracking API usage and billing information
/// </summary>
public interface IUsageTrackingService
{
    /// <summary>
    /// Track an API call for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="operationType">Type of operation (e.g., "invoice_purchase", "price_check")</param>
    /// <param name="success">Whether the operation was successful</param>
    /// <param name="responseTimeMs">Response time in milliseconds</param>
    /// <param name="dataTransferBytes">Data transfer size in bytes</param>
    /// <param name="metadata">Additional metadata (JSON)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Tracking result</returns>
    Task<Response<bool>> TrackApiCallAsync(
        Guid partnerId,
        string operationType,
        bool success,
        double responseTimeMs,
        long dataTransferBytes = 0,
        string? metadata = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Track invoice purchase for billing
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="invoiceCount">Number of invoices purchased</param>
    /// <param name="totalAmount">Total amount charged</param>
    /// <param name="invoiceType">Type of invoices purchased</param>
    /// <param name="metadata">Additional metadata (JSON)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Tracking result</returns>
    Task<Response<bool>> TrackInvoicePurchaseAsync(
        Guid partnerId,
        int invoiceCount,
        decimal totalAmount,
        string invoiceType,
        string? metadata = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get usage statistics for a partner in a specific period
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="period">Period in YYYY-MM format</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Usage statistics</returns>
    Task<Response<UsageStatistics?>> GetUsageStatisticsAsync(
        Guid partnerId,
        string period,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current month usage for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current month usage</returns>
    Task<Response<UsageStatistics?>> GetCurrentMonthUsageAsync(
        Guid partnerId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get usage history for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="fromPeriod">Start period (YYYY-MM)</param>
    /// <param name="toPeriod">End period (YYYY-MM)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Usage history</returns>
    Task<Response<IEnumerable<UsageStatistics>>> GetUsageHistoryAsync(
        Guid partnerId,
        string fromPeriod,
        string toPeriod,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get API call count for rate limiting
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="sinceDatetime">Count calls since this datetime</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>API call count</returns>
    Task<Response<int>> GetApiCallCountAsync(
        Guid partnerId,
        DateTime sinceDatetime,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current hour API call count for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current hour API call count</returns>
    Task<Response<int>> GetCurrentHourApiCallCountAsync(
        Guid partnerId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate usage report for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="reportType">Type of report ("monthly", "daily", "hourly")</param>
    /// <param name="period">Period for the report</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Usage report</returns>
    Task<Response<UsageReport>> GenerateUsageReportAsync(
        Guid partnerId,
        string reportType,
        string period,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Update partner's current month usage
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="additionalAmount">Additional amount to add to current usage</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Update result</returns>
    Task<Response<bool>> UpdateCurrentMonthUsageAsync(
        Guid partnerId,
        decimal additionalAmount,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Usage statistics for a specific period
/// </summary>
public class UsageStatistics
{
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = null!;
    public string Period { get; set; } = null!;
    public int InvoicesPurchased { get; set; }
    public decimal TotalAmount { get; set; }
    public long ApiCallsCount { get; set; }
    public long SuccessfulApiCalls { get; set; }
    public long FailedApiCalls { get; set; }
    public long DataTransferBytes { get; set; }
    public int PeakRequestsPerHour { get; set; }
    public double AverageResponseTimeMs { get; set; }
    public Dictionary<string, int> OperationCounts { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

/// <summary>
/// Detailed usage report
/// </summary>
public class UsageReport
{
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = null!;
    public string ReportType { get; set; } = null!;
    public string Period { get; set; } = null!;
    public DateTime GeneratedAt { get; set; }
    public UsageStatistics Summary { get; set; } = null!;
    public Dictionary<string, object> DetailedMetrics { get; set; } = new();
    public string[] TopOperations { get; set; } = Array.Empty<string>();
    public Dictionary<string, decimal> CostBreakdown { get; set; } = new();
}