using Shared.Responses;

namespace Applications.Interfaces.Services.Authorization;

/// <summary>
/// Interface for business rule validation and constraint checking
/// </summary>
public interface IConstraintService
{
    /// <summary>
    /// Validate a constraint for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="constraintType">Type of constraint to validate</param>
    /// <param name="currentValue">Current value to check against constraint</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Constraint validation result</returns>
    Task<Response<ConstraintValidationResult>> ValidateConstraintAsync(
        Guid partnerId,
        string constraintType,
        object currentValue,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check multiple constraints at once
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="constraintChecks">List of constraints to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Multiple constraint validation results</returns>
    Task<Response<Dictionary<string, ConstraintValidationResult>>> ValidateMultipleConstraintsAsync(
        Guid partnerId,
        IEnumerable<ConstraintCheck> constraintChecks,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all active constraints for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of partner constraints</returns>
    Task<Response<PartnerConstraintSummary>> GetPartnerConstraintsAsync(
        Guid partnerId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Add or update a constraint for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="constraintType">Type of constraint</param>
    /// <param name="constraintValue">Constraint value (JSON)</param>
    /// <param name="validFrom">When constraint becomes active</param>
    /// <param name="validTo">When constraint expires (optional)</param>
    /// <param name="setBy">Who set this constraint</param>
    /// <param name="description">Description/reason</param>
    /// <param name="priority">Constraint priority</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Set constraint result</returns>
    Task<Response<bool>> SetConstraintAsync(
        Guid partnerId,
        string constraintType,
        string constraintValue,
        DateTime validFrom,
        DateTime? validTo,
        Guid setBy,
        string? description = null,
        int priority = 0,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove a constraint for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="constraintType">Type of constraint to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Remove constraint result</returns>
    Task<Response<bool>> RemoveConstraintAsync(
        Guid partnerId,
        string constraintType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if partner has reached their monthly invoice limit
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="additionalAmount">Additional amount to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Monthly limit check result</returns>
    Task<Response<MonthlyLimitResult>> CheckMonthlyLimitAsync(
        Guid partnerId,
        decimal additionalAmount,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check API rate limit for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Rate limit check result</returns>
    Task<Response<RateLimitResult>> CheckRateLimitAsync(
        Guid partnerId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Clear cached constraints for a partner
    /// </summary>
    /// <param name="partnerId">Partner ID</param>
    void ClearConstraintCache(Guid partnerId);
}

/// <summary>
/// Constraint check request
/// </summary>
public class ConstraintCheck
{
    public string ConstraintType { get; set; } = null!;
    public object CurrentValue { get; set; } = null!;
    public string? CheckKey { get; set; } // Optional key to identify this check in bulk results
}

/// <summary>
/// Result of constraint validation
/// </summary>
public class ConstraintValidationResult
{
    public bool IsValid { get; set; }
    public string ConstraintType { get; set; } = null!;
    public object CurrentValue { get; set; } = null!;
    public object? LimitValue { get; set; }
    public string? ErrorMessage { get; set; }
    public string? Details { get; set; }
    public DateTime? NextAllowedTime { get; set; }
}

/// <summary>
/// Summary of all constraints for a partner
/// </summary>
public class PartnerConstraintSummary
{
    public Guid PartnerId { get; set; }
    public string PartnerName { get; set; } = null!;
    public Dictionary<string, ConstraintInfo> Constraints { get; set; } = new();
    public DateTime CacheTime { get; set; }
}

/// <summary>
/// Information about a specific constraint
/// </summary>
public class ConstraintInfo
{
    public string ConstraintType { get; set; } = null!;
    public string ConstraintValue { get; set; } = null!;
    public DateTime ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }
    public int Priority { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Result of monthly limit check
/// </summary>
public class MonthlyLimitResult
{
    public bool IsWithinLimit { get; set; }
    public decimal CurrentUsage { get; set; }
    public decimal MonthlyLimit { get; set; }
    public decimal RemainingLimit { get; set; }
    public decimal RequestedAmount { get; set; }
    public string CurrentPeriod { get; set; } = null!;
}

/// <summary>
/// Result of rate limit check
/// </summary>
public class RateLimitResult
{
    public bool IsWithinLimit { get; set; }
    public int CurrentRequestCount { get; set; }
    public int HourlyLimit { get; set; }
    public int RemainingRequests { get; set; }
    public DateTime ResetTime { get; set; }
}