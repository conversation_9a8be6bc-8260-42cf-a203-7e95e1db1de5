# 🧪 Kịch bản Test API Authentication/Authorization Flow

## 📋 Thông tin quan trọng

### 📍 Vị trí các file quan trọng:
- **SQL Seed Data**: `/Scripts/SimpleSeedData.sql` (dữ liệu test đơn giản)
- **SQL Full Data**: `/Scripts/CorrectSeedData.sql` (dữ liệu đầy đủ)
- **API URL**: `https://localhost:5001` hoặc `http://localhost:5000`
- **Swagger**: `https://localhost:5001/swagger`

### 🔑 Test Credentials:
- **Admin**: `test_admin` / `admin123`
- **User**: `test_user` / `user123`

### ⚙️ Cấu hình hiện tại:
- ✅ IP Whitelist: **TẮT** (EnableIpWhitelist = false)
- ✅ Timestamp Validation: **TẮT** (EnableTimestampValidation = false)
- ✅ Headers bắt buộc: `Authorization`, `X-Signature`, `X-Client-Id`
- ❌ Headers không bắt buộc: `X-Timestamp` (vì đã tắt validation)

## 🚀 Các bước thực hiện

### **Bước 1: Chuẩn bị Database**
```sql
-- Chạy script seed data (chọn 1 trong 2):
-- Option 1: Dữ liệu đơn giản cho test
\i /path/to/Scripts/SimpleSeedData.sql

-- Option 2: Dữ liệu đầy đủ
\i /path/to/Scripts/CorrectSeedData.sql
```

### **Bước 2: Khởi động API**
```bash
cd API
dotnet run --urls "https://localhost:5001;http://localhost:5000"
```

### **Bước 3: Test Authentication Endpoints (Không cần token)**

#### 3.1. Lấy Timestamp (Optional)
```bash
curl -X GET "https://localhost:5001/api/authentication/timestamp" -k
```

**Expected Response:**
```json
{
  "code": "000",
  "message": "Current timestamp",
  "data": {
    "timestamp": "1751703253",
    "serverTime": "2025-07-05T08:14:13Z",
    "validitySeconds": 300
  },
  "isSuccess": true
}
```

#### 3.2. Check IP (Optional)
```bash
curl -X GET "https://localhost:5001/api/authentication/ip-check" -k
```

#### 3.3. **Lấy Access Token (OAuth2)**
```bash
# Test với Admin
curl -X POST "https://localhost:5001/api/authentication/token" \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "test_admin",
    "clientSecret": "admin123",
    "grantType": "client_credentials"
  }' \
  -k

# Test với User
curl -X POST "https://localhost:5001/api/authentication/token" \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "test_user", 
    "clientSecret": "user123",
    "grantType": "client_credentials"
  }' \
  -k
```

**Expected Response:**
```json
{
  "code": "000",
  "message": "Token issued successfully",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 7200,
    "scope": "invoice_create invoice_read mobifone_api"
  },
  "isSuccess": true
}
```

### **Bước 4: Test Protected APIs**

#### 4.1. Chuẩn bị Headers
```bash
# Lưu token từ bước 3.3
TOKEN="YOUR_ACCESS_TOKEN_HERE"
CLIENT_ID="test_admin"  # hoặc "test_user"

# Tạo HMAC signature (simplified - trong thực tế cần tính toán chính xác)
TIMESTAMP="1751703253"  # từ endpoint timestamp hoặc current unix timestamp
SIGNATURE="dummy_signature_for_test"  # Có thể dùng giá trị dummy nếu tắt validation
```

#### 4.2. Test MobiFone APIs (Cần MOBIFONE_API permission)
```bash
# Admin có quyền - Should work ✅
curl -X POST "https://localhost:5001/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Client-Id: $CLIENT_ID" \
  -H "X-Signature: $SIGNATURE" \
  -H "X-Token: dummy_mobifone_token" \
  -H "X-MaDvcs: TEST001" \
  -H "Content-Type: application/json" \
  -d '{"username": "test", "password": "test"}' \
  -k

# User có quyền - Should work ✅  
# (Dùng token của test_user)
```

#### 4.3. Test Client Credential APIs (Cần ADMIN_MANAGE permission)
```bash
# Admin có quyền - Should work ✅
curl -X GET "https://localhost:5001/api/client-credential" \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Client-Id: $CLIENT_ID" \
  -H "X-Signature: $SIGNATURE" \
  -k

# User KHÔNG có quyền - Should fail ❌ (403 Forbidden)
# (Dùng token của test_user)
```

### **Bước 5: Test Authorization Scenarios**

#### 5.1. Valid Admin Access
```bash
# Admin token + Admin endpoint = ✅ SUCCESS
curl -X GET "https://localhost:5001/api/client-credential" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "X-Client-Id: test_admin" \
  -H "X-Signature: dummy_signature" \
  -k
```
**Expected**: 200 OK với data

#### 5.2. Invalid User Access
```bash
# User token + Admin endpoint = ❌ FORBIDDEN
curl -X GET "https://localhost:5001/api/client-credential" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "X-Client-Id: test_user" \
  -H "X-Signature: dummy_signature" \
  -k
```
**Expected**: 403 Forbidden

#### 5.3. Missing Headers
```bash
# Missing Authorization = ❌ UNAUTHORIZED
curl -X GET "https://localhost:5001/api/client-credential" \
  -H "X-Client-Id: test_admin" \
  -H "X-Signature: dummy_signature" \
  -k
```
**Expected**: 401 Unauthorized

#### 5.4. Invalid Token
```bash
# Invalid token = ❌ UNAUTHORIZED
curl -X GET "https://localhost:5001/api/client-credential" \
  -H "Authorization: Bearer invalid_token_here" \
  -H "X-Client-Id: test_admin" \
  -H "X-Signature: dummy_signature" \
  -k
```
**Expected**: 401 Unauthorized

## 📊 Kết quả mong đợi

### ✅ SUCCESS Cases:
- Admin token + Any endpoint = 200 OK
- User token + User-allowed endpoints = 200 OK
- All authentication endpoints work without token
- Swagger accessible without authentication

### ❌ FAILURE Cases:
- User token + Admin endpoints = 403 Forbidden
- No token + Protected endpoints = 401 Unauthorized
- Invalid token = 401 Unauthorized
- Missing required headers = 400 Bad Request

## 🔧 Troubleshooting

### Nếu gặp lỗi 500 Internal Server Error:
1. Check database connection
2. Check if seed data đã chạy thành công
3. Check application logs

### Nếu gặp lỗi HMAC Signature:
1. Đảm bảo `EnableTimestampValidation = false` trong config
2. Có thể dùng dummy signature cho test
3. Nếu muốn test real signature, implement HMAC calculation

### Nếu muốn bật lại security features:
```csharp
// Trong API/Program.cs
options.EnableIpWhitelist = true;  // Bật IP whitelist
options.EnableTimestampValidation = true;  // Bật timestamp validation
```

## 📖 Swagger Documentation

Truy cập: `https://localhost:5001/swagger`

Swagger sẽ hiển thị:
- ✅ All required headers for each endpoint
- ✅ Bearer token authentication
- ✅ HMAC signature documentation
- ✅ Example requests and responses

## 🎯 Test Checklist

- [ ] Database seeded successfully
- [ ] API starts without errors
- [ ] Token endpoint works (step 3.3)
- [ ] Admin token allows access to all endpoints
- [ ] User token denied access to admin endpoints
- [ ] Invalid tokens rejected properly
- [ ] Swagger shows all required headers
- [ ] All middleware working in correct order