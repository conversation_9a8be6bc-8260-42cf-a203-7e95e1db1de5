# 🛍️ ZenShop → Authentication → MobiFone API Test Scenario

## 📋 Thông tin quan trọng

### 🔑 ZenShop Test Credentials:
- **Client ID**: `zenshop_client`
- **Client Secret**: `zenshop123`
- **Role**: Premium Partner (có quyền gọi MobiFone APIs)

### 🎯 Mục tiêu test:
**ZenShop** → **Authentication/Authorization** → **MobiFone Login API** ✅

## 🚀 Kịch bản test đầy đủ

### **Bước 1: Seed Database**
```sql
-- Chạy file CorrectSeedData.sql đã được sửa
\i /path/to/Scripts/CorrectSeedData.sql

-- Verify data
SELECT 
    p."Name" as partner_name,
    p."ClientId" as client_id,
    pr."Name" as role_name,
    p."IsActive" as is_active
FROM "Partners" p
LEFT JOIN "PartnerRoleAssignments" pra ON p."Id" = pra."PartnerId" AND pra."IsActive" = true
LEFT JOIN "PartnerRoles" pr ON pra."RoleId" = pr."Id"
WHERE p."ClientId" = 'zenshop_client';
```

### **Bước 2: ZenShop Authentication**
```bash
# ZenShop đăng nhập để lấy access token
curl -X POST "https://localhost:5001/api/authentication/token" \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "zenshop_client",
    "clientSecret": "zenshop123",
    "grantType": "client_credentials"
  }' \
  -k
```

**Expected Response:**
```json
{
  "code": "000",
  "message": "Token issued successfully",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 7200,
    "scope": "invoice_create invoice_read invoice_update invoice_sign invoice_send_cqt mobifone_api report_view"
  },
  "isSuccess": true
}
```

### **Bước 3: ZenShop → MobiFone Login API**
```bash
# Lưu token từ bước 2
ZENSHOP_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# ZenShop gọi MobiFone login API
curl -X POST "https://localhost:5001/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $ZENSHOP_TOKEN" \
  -H "X-Client-Id: zenshop_client" \
  -H "X-Signature: dummy_signature_for_test" \
  -H "X-Token: dummy_mobifone_token" \
  -H "X-MaDvcs: ZENSHOP001" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "zenshop_user",
    "password": "zenshop_pass"
  }' \
  -k
```

**Expected Response (Success):**
```json
{
  "code": "000", 
  "message": "MobiFone login successful",
  "data": {
    "token": "mobifone_access_token_here",
    "expiresIn": 3600,
    "userInfo": {
      "username": "zenshop_user",
      "permissions": ["invoice_create", "invoice_read"]
    }
  },
  "isSuccess": true
}
```

### **Bước 4: Tiếp tục với các MobiFone APIs khác**
```bash
# Sau khi login thành công, ZenShop có thể gọi các API khác
# 1. Tạo hóa đơn
curl -X POST "https://localhost:5001/api/mobifone-invoice/create-invoice" \
  -H "Authorization: Bearer $ZENSHOP_TOKEN" \
  -H "X-Client-Id: zenshop_client" \
  -H "X-Signature: dummy_signature" \
  -H "X-Token: mobifone_token_from_login" \
  -H "X-MaDvcs: ZENSHOP001" \
  -H "Content-Type: application/json" \
  -d '{
    "customerName": "Nguyen Van A",
    "amount": 1000000,
    "items": [{"name": "Product A", "quantity": 1, "price": 1000000}]
  }' \
  -k

# 2. Lấy lịch sử hóa đơn
curl -X GET "https://localhost:5001/api/mobifone-invoice/get-history-invoice?fromDate=2025-01-01&toDate=2025-12-31" \
  -H "Authorization: Bearer $ZENSHOP_TOKEN" \
  -H "X-Client-Id: zenshop_client" \
  -H "X-Signature: dummy_signature" \
  -H "X-Token: mobifone_token_from_login" \
  -H "X-MaDvcs: ZENSHOP001" \
  -k
```

## 🧪 Test Cases cần kiểm tra

### ✅ **Success Cases:**
1. **ZenShop Authentication** → Token thành công
2. **ZenShop → MobiFone Login** → Login thành công  
3. **ZenShop → MobiFone APIs** → Các API khác hoạt động
4. **Permission Check** → ZenShop có đủ quyền MOBIFONE_API

### ❌ **Failure Cases:**
1. **Invalid Credentials** → 401 Unauthorized
2. **Expired Token** → 401 Unauthorized
3. **Missing Headers** → 400 Bad Request
4. **Insufficient Permissions** → 403 Forbidden (test với test_client)

### 🔄 **Edge Cases:**
1. **Token Expiration** → Refresh token mechanism
2. **Rate Limiting** → 429 Too Many Requests
3. **Concurrent Requests** → Multiple ZenShop instances

## 📊 Verification Checklist

- [ ] Database seeded successfully
- [ ] ZenShop client exists with Premium Partner role
- [ ] ZenShop authentication successful (step 2)
- [ ] ZenShop can access MobiFone login API (step 3)
- [ ] ZenShop can access other MobiFone APIs (step 4)
- [ ] Error handling works properly
- [ ] Logs tracking API usage correctly

## 🚨 Troubleshooting

### Nếu Authentication fails:
1. Check database có data ZenShop client chưa
2. Verify password hash đúng format
3. Check API service đang chạy

### Nếu MobiFone API fails:
1. Check ZenShop có role Premium Partner chưa
2. Verify quyền MOBIFONE_API được assigned
3. Check required headers đầy đủ chưa

### Nếu muốn test với credentials khác:
```sql
-- Tạo thêm partner test
INSERT INTO "Partners" (...) VALUES (...);
-- Gán role và permissions tương ứng
```

---

## 🔧 Đề xuất API quan trọng bổ sung

### 1. **Session Management APIs**
```
GET  /api/authentication/sessions/{partnerId}     # List active sessions
POST /api/authentication/sessions/refresh        # Refresh token
DELETE /api/authentication/sessions/{sessionId}  # Logout/revoke session
```

### 2. **Partner Management APIs** (cho Admin)
```
GET    /api/admin/partners                       # List all partners
POST   /api/admin/partners                       # Create new partner
PUT    /api/admin/partners/{id}                  # Update partner
DELETE /api/admin/partners/{id}                  # Deactivate partner
POST   /api/admin/partners/{id}/reset-secret     # Reset client secret
```

### 3. **Permission Management APIs**
```
GET  /api/admin/partners/{id}/permissions        # Get partner permissions
POST /api/admin/partners/{id}/permissions        # Grant permissions
DELETE /api/admin/partners/{id}/permissions/{permissionId} # Revoke permission
```

### 4. **Usage Analytics APIs**
```
GET /api/admin/usage/partners/{id}               # Partner usage stats
GET /api/admin/usage/apis                        # API usage summary
GET /api/admin/usage/reports                     # Usage reports
```

### 5. **Health Check & Monitoring**
```
GET /api/health                                  # Basic health check
GET /api/health/dependencies                     # Check database, external APIs
GET /api/metrics                                 # Prometheus metrics
```

### 6. **Webhook APIs** (cho real-time notifications)
```
POST /api/webhooks/partner-events               # Partner status changes
POST /api/webhooks/usage-alerts                 # Usage limit alerts
POST /api/webhooks/security-events              # Security incidents
```

**Những API nào bạn muốn tôi implement trước?** 🤔