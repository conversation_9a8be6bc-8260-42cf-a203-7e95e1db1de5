#!/bin/bash

# =================================================================
# FULL SECURITY AUTHENTICATION/AUTHORIZATION TEST
# =================================================================
# Middleware Pipeline được kích hoạt:
# 1. IpWhitelistMiddleware ✅
# 2. JwtAuthenticationMiddleware ✅
# 3. SignatureValidationMiddleware ✅
# 4. PermissionAuthorizationMiddleware ✅
# 5. UsageTrackingMiddleware ✅
# =================================================================

API_URL="https://localhost:5001"
CLIENT_ID="zenshop_client"
CLIENT_SECRET="password"

echo "🔒 FULL SECURITY AUTHENTICATION/AUTHORIZATION TEST"
echo "📍 API URL: $API_URL"
echo "🏢 Client: $CLIENT_ID"
echo

# Function to create HMAC signature
create_signature() {
    local http_method="$1"
    local request_path="$2"
    local timestamp="$3"
    local client_id="$4"
    local payload="$5"
    local hmac_secret="password"  # ZenShop's HMAC secret
    
    # StringToSign = HTTP_METHOD + "\n" + REQUEST_PATH + "\n" + TIMESTAMP + "\n" + CLIENT_ID + "\n" + PAYLOAD
    local string_to_sign="${http_method}\n${request_path}\n${timestamp}\n${client_id}\n${payload}"
    
    # Generate HMAC-SHA256 and encode to base64
    echo -n "$string_to_sign" | openssl dgst -sha256 -hmac "$hmac_secret" -binary | base64
}

# =================================================================
# STEP 1: IP WHITELIST TEST (Middleware 1)
# =================================================================
echo "=== 🔐 STEP 1: IP WHITELIST VALIDATION ==="
echo "📋 Testing IP whitelist middleware..."
echo "🎯 Expected: Should pass (localhost in whitelist)"

# Create timestamp and signature for authentication request
AUTH_TIMESTAMP=$(date +%s)
AUTH_PAYLOAD='{"clientId":"'$CLIENT_ID'","clientSecret":"'$CLIENT_SECRET'","grantType":"client_credentials"}'
AUTH_SIGNATURE=$(create_signature "POST" "/api/authentication/token" "$AUTH_TIMESTAMP" "$CLIENT_ID" "$AUTH_PAYLOAD")

echo "🔍 IP Whitelist test: ZenShop authentication from localhost..."
IP_TEST_RESPONSE=$(curl -s -X POST "$API_URL/api/authentication/token" \
  -H "Content-Type: application/json" \
  -H "X-Client-ID: $CLIENT_ID" \
  -H "X-Timestamp: $AUTH_TIMESTAMP" \
  -H "X-Signature: $AUTH_SIGNATURE" \
  -d "$AUTH_PAYLOAD" \
  -k)

echo "📋 Response:"
echo "$IP_TEST_RESPONSE" | jq '.'

IP_SUCCESS=$(echo "$IP_TEST_RESPONSE" | jq -r '.isSuccess // false')
if [ "$IP_SUCCESS" = "true" ]; then
    echo "✅ IP Whitelist PASS - localhost allowed"
    ACCESS_TOKEN=$(echo "$IP_TEST_RESPONSE" | jq -r '.data.accessToken')
else
    echo "❌ IP Whitelist FAILED - check IP configuration"
    echo "🛑 Cannot proceed to next steps"
    exit 1
fi
echo

# =================================================================
# STEP 2: JWT AUTHENTICATION TEST (Middleware 2)
# =================================================================
echo "=== 🔑 STEP 2: JWT AUTHENTICATION VALIDATION ==="
echo "📋 Testing JWT authentication middleware..."
echo "🎯 Expected: Should validate JWT token"

if [ -n "$ACCESS_TOKEN" ]; then
    echo "✅ JWT Token obtained: ${ACCESS_TOKEN:0:50}..."
    echo "📋 Token scope: $(echo "$IP_TEST_RESPONSE" | jq -r '.data.scope')"
else
    echo "❌ No JWT token available"
    exit 1
fi
echo

# =================================================================
# STEP 3: SIGNATURE VALIDATION TEST (Middleware 3)
# =================================================================
echo "=== 🔏 STEP 3: HMAC SIGNATURE VALIDATION ==="
echo "📋 Testing HMAC signature middleware..."
echo "🎯 Expected: Should validate HMAC-SHA256 signature"

# Create proper signature for MobiFone login API
API_TIMESTAMP=$(date +%s)
API_PAYLOAD='{"username":"test_user","password":"test_password"}'
API_SIGNATURE=$(create_signature "POST" "/api/mobifone-invoice/login" "$API_TIMESTAMP" "$CLIENT_ID" "$API_PAYLOAD")

echo "🔍 Testing with VALID signature..."
echo "📊 Signature calculation:"
echo "  Method: POST"
echo "  Path: /api/mobifone-invoice/login"
echo "  Timestamp: $API_TIMESTAMP"
echo "  Client: $CLIENT_ID"
echo "  Payload: $API_PAYLOAD"
echo "  Signature: $API_SIGNATURE"

SIGNATURE_TEST_RESPONSE=$(curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "X-Client-ID: $CLIENT_ID" \
  -H "X-Timestamp: $API_TIMESTAMP" \
  -H "X-Signature: $API_SIGNATURE" \
  -H "Content-Type: application/json" \
  -d "$API_PAYLOAD" \
  -k)

echo "📋 Response:"
echo "$SIGNATURE_TEST_RESPONSE" | jq '.'

SIGNATURE_CODE=$(echo "$SIGNATURE_TEST_RESPONSE" | jq -r '.Code // .code // "unknown"')
if [ "$SIGNATURE_CODE" != "401" ] && [ "$SIGNATURE_CODE" != "403" ]; then
    echo "✅ HMAC Signature PASS - signature validated"
else
    echo "❌ HMAC Signature FAILED - signature validation error"
    echo "🔍 Check HMAC secret and signature calculation"
fi
echo

# =================================================================
# STEP 4: PERMISSION AUTHORIZATION TEST (Middleware 4)
# =================================================================
echo "=== 🛡️ STEP 4: PERMISSION AUTHORIZATION VALIDATION ==="
echo "📋 Testing permission authorization middleware..."
echo "🎯 Expected: Should check MOBIFONE_API_EXECUTE permission"

echo "🔍 Testing MobiFone API access (should have permission)..."
PERMISSION_CODE=$(echo "$SIGNATURE_TEST_RESPONSE" | jq -r '.Code // .code // "unknown"')
if [ "$PERMISSION_CODE" = "403" ]; then
    echo "❌ Permission DENIED - ZenShop missing MOBIFONE_API_EXECUTE"
elif [ "$PERMISSION_CODE" = "401" ]; then
    echo "❌ Authentication FAILED in middleware pipeline"
else
    echo "✅ Permission GRANTED - ZenShop has MOBIFONE_API_EXECUTE"
fi

echo "🔍 Testing admin API access (should be denied)..."
ADMIN_SIGNATURE=$(create_signature "GET" "/api/client-credential" "$API_TIMESTAMP" "$CLIENT_ID" "")
ADMIN_TEST_RESPONSE=$(curl -s -X GET "$API_URL/api/client-credential" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "X-Client-ID: $CLIENT_ID" \
  -H "X-Timestamp: $API_TIMESTAMP" \
  -H "X-Signature: $ADMIN_SIGNATURE" \
  -k)

echo "📋 Admin API Response:"
echo "$ADMIN_TEST_RESPONSE" | jq '.'

ADMIN_CODE=$(echo "$ADMIN_TEST_RESPONSE" | jq -r '.Code // .code // "unknown"')
if [ "$ADMIN_CODE" = "403" ]; then
    echo "✅ Permission Control WORKING - admin access denied"
else
    echo "⚠️ Permission Control might not be working: $ADMIN_CODE"
fi
echo

# =================================================================
# STEP 5: USAGE TRACKING TEST (Middleware 5)
# =================================================================
echo "=== 📊 STEP 5: USAGE TRACKING VALIDATION ==="
echo "📋 Testing usage tracking middleware..."
echo "🎯 Expected: Should log API usage for rate limiting"

echo "🔍 Making multiple API calls to test usage tracking..."
for i in {1..3}; do
    echo "  API call #$i..."
    USAGE_TIMESTAMP=$(date +%s)
    USAGE_SIGNATURE=$(create_signature "POST" "/api/mobifone-invoice/login" "$USAGE_TIMESTAMP" "$CLIENT_ID" "$API_PAYLOAD")
    
    curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
      -H "Authorization: Bearer $ACCESS_TOKEN" \
      -H "X-Client-ID: $CLIENT_ID" \
      -H "X-Timestamp: $USAGE_TIMESTAMP" \
      -H "X-Signature: $USAGE_SIGNATURE" \
      -H "Content-Type: application/json" \
      -d "$API_PAYLOAD" \
      -k > /dev/null
    
    sleep 1
done

echo "✅ Usage Tracking ENABLED - API calls logged for rate limiting"
echo

# =================================================================
# STEP 6: NEGATIVE TESTS
# =================================================================
echo "=== ❌ STEP 6: NEGATIVE TESTS ==="

echo "🔍 6.1. Testing INVALID signature..."
INVALID_SIGNATURE="invalid_signature_123"
INVALID_SIG_RESPONSE=$(curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "X-Client-ID: $CLIENT_ID" \
  -H "X-Timestamp: $API_TIMESTAMP" \
  -H "X-Signature: $INVALID_SIGNATURE" \
  -H "Content-Type: application/json" \
  -d "$API_PAYLOAD" \
  -k)

INVALID_SIG_CODE=$(echo "$INVALID_SIG_RESPONSE" | jq -r '.Code // .code // "unknown"')
echo "📋 Invalid signature response: $INVALID_SIG_CODE"
if [ "$INVALID_SIG_CODE" = "401" ]; then
    echo "✅ Signature validation WORKING - invalid signature rejected"
else
    echo "⚠️ Signature validation might not be working"
fi

echo "🔍 6.2. Testing EXPIRED timestamp..."
OLD_TIMESTAMP=$(($(date +%s) - 600))  # 10 minutes ago
OLD_SIGNATURE=$(create_signature "POST" "/api/mobifone-invoice/login" "$OLD_TIMESTAMP" "$CLIENT_ID" "$API_PAYLOAD")
OLD_TIME_RESPONSE=$(curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "X-Client-ID: $CLIENT_ID" \
  -H "X-Timestamp: $OLD_TIMESTAMP" \
  -H "X-Signature: $OLD_SIGNATURE" \
  -H "Content-Type: application/json" \
  -d "$API_PAYLOAD" \
  -k)

OLD_TIME_CODE=$(echo "$OLD_TIME_RESPONSE" | jq -r '.Code // .code // "unknown"')
echo "📋 Expired timestamp response: $OLD_TIME_CODE"
if [ "$OLD_TIME_CODE" = "401" ]; then
    echo "✅ Timestamp validation WORKING - expired timestamp rejected"
else
    echo "⚠️ Timestamp validation might not be working"
fi

echo "🔍 6.3. Testing INVALID token..."
INVALID_TOKEN_RESPONSE=$(curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "Authorization: Bearer invalid_token_123" \
  -H "X-Client-ID: $CLIENT_ID" \
  -H "X-Timestamp: $API_TIMESTAMP" \
  -H "X-Signature: $API_SIGNATURE" \
  -H "Content-Type: application/json" \
  -d "$API_PAYLOAD" \
  -k)

INVALID_TOKEN_CODE=$(echo "$INVALID_TOKEN_RESPONSE" | jq -r '.Code // .code // "unknown"')
echo "📋 Invalid token response: $INVALID_TOKEN_CODE"
if [ "$INVALID_TOKEN_CODE" = "401" ]; then
    echo "✅ JWT validation WORKING - invalid token rejected"
else
    echo "⚠️ JWT validation might not be working"
fi
echo

# =================================================================
# FULL SECURITY TEST SUMMARY
# =================================================================
echo "=== 🎯 FULL SECURITY TEST SUMMARY ==="
echo "🔒 Security Middleware Pipeline Results:"
echo "  1️⃣ IP Whitelist: $([ "$IP_SUCCESS" = "true" ] && echo "✅ PASS" || echo "❌ FAIL")"
echo "  2️⃣ JWT Authentication: $([ -n "$ACCESS_TOKEN" ] && echo "✅ PASS" || echo "❌ FAIL")"
echo "  3️⃣ HMAC Signature: $([ "$SIGNATURE_CODE" != "401" ] && echo "✅ PASS" || echo "❌ FAIL")"
echo "  4️⃣ Permission Authorization: $([ "$PERMISSION_CODE" != "403" ] && echo "✅ PASS" || echo "❌ FAIL")"
echo "  5️⃣ Usage Tracking: ✅ ENABLED"
echo

echo "🔐 Security Validations:"
echo "  ├─ Invalid Signature: $([ "$INVALID_SIG_CODE" = "401" ] && echo "✅ REJECTED" || echo "⚠️ ALLOWED")"
echo "  ├─ Expired Timestamp: $([ "$OLD_TIME_CODE" = "401" ] && echo "✅ REJECTED" || echo "⚠️ ALLOWED")"
echo "  ├─ Invalid Token: $([ "$INVALID_TOKEN_CODE" = "401" ] && echo "✅ REJECTED" || echo "⚠️ ALLOWED")"
echo "  └─ Admin Access: $([ "$ADMIN_CODE" = "403" ] && echo "✅ DENIED" || echo "⚠️ ALLOWED")"
echo

echo "🚀 FULL SECURITY FLOW:"
echo "  1️⃣ Client from whitelisted IP calls /api/authentication/token"
echo "  2️⃣ System validates IP → HMAC signature → credentials"
echo "  3️⃣ System returns JWT token with scopes"
echo "  4️⃣ Client calls API with JWT + HMAC signature"
echo "  5️⃣ System validates IP → JWT → HMAC → permissions → logs usage"
echo "  6️⃣ API processes business logic or returns error"
echo

if [ "$IP_SUCCESS" = "true" ] && [ -n "$ACCESS_TOKEN" ] && [ "$SIGNATURE_CODE" != "401" ]; then
    echo "🎉 FULL SECURITY AUTHENTICATION/AUTHORIZATION IS WORKING!"
else
    echo "❌ Some security components need attention"
fi