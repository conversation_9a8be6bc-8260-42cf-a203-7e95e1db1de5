using Applications.Interfaces.Services.Authorization;
using Core.Entities.Authentication;
using Infrastructure.Persistences;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Responses;

namespace Infrastructure.Services.Authorization;

/// <summary>
/// Implementation of role management service
/// </summary>
public class RoleService : IRoleService
{
    private readonly ILogger<RoleService> _logger;
    private readonly AppDbContext _context;

    public RoleService(ILogger<RoleService> logger, AppDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<Response<IEnumerable<PartnerRoleInfo>>> GetPartnerRolesAsync(
        Guid partnerId, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting roles for partner {PartnerId}", partnerId);
        
        try
        {
            var partnerRoles = await _context.PartnerRoleAssignments
                .Include(pra => pra.Role)
                .Where(pra => 
                    pra.PartnerId == partnerId && 
                    pra.IsActive && 
                    !pra.IsDeleted &&
                    (pra.ExpiresAt == null || pra.ExpiresAt > DateTime.UtcNow))
                .Select(pra => new PartnerRoleInfo
                {
                    RoleId = pra.RoleId,
                    RoleCode = pra.Role.Code,
                    RoleName = pra.Role.Name,
                    AssignedAt = pra.AssignedAt,
                    ExpiresAt = pra.ExpiresAt,
                    AssignmentReason = pra.AssignmentReason,
                    IsActive = pra.IsActive
                })
                .ToArrayAsync(cancellationToken);

            _logger.LogInformation("Found {Count} active roles for partner {PartnerId}", 
                partnerRoles.Length, partnerId);

            return Response<IEnumerable<PartnerRoleInfo>>.Success(
                partnerRoles.AsEnumerable(), $"Retrieved {partnerRoles.Length} roles");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting roles for partner {PartnerId}", partnerId);
            return Response<IEnumerable<PartnerRoleInfo>>.Failure<IEnumerable<PartnerRoleInfo>>(
                "Error retrieving partner roles", "500");
        }
    }

    public async Task<Response<bool>> AssignRoleAsync(
        Guid partnerId, 
        string roleCode, 
        Guid assignedBy, 
        string reason, 
        DateTime? expiresAt = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Assigning role {RoleCode} to partner {PartnerId} by {AssignedBy}", roleCode, partnerId, assignedBy);
        
        try
        {
            // Check if role exists
            var role = await _context.PartnerRoles
                .Where(r => r.Code == roleCode && r.IsActive && !r.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (role == null)
            {
                return Response<bool>.Failure<bool>($"Role {roleCode} not found", "404");
            }

            // Check if partner exists
            var partner = await _context.Partners
                .Where(p => p.Id == partnerId && p.IsActive && !p.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (partner == null)
            {
                return Response<bool>.Failure<bool>("Partner not found", "404");
            }

            // Check if assignment already exists and is active
            var existingAssignment = await _context.PartnerRoleAssignments
                .Where(pra => 
                    pra.PartnerId == partnerId && 
                    pra.RoleId == role.Id &&
                    pra.IsActive && 
                    !pra.IsDeleted &&
                    (pra.ExpiresAt == null || pra.ExpiresAt > DateTime.UtcNow))
                .FirstOrDefaultAsync(cancellationToken);

            if (existingAssignment != null)
            {
                return Response<bool>.Success(true, "Role already assigned to partner");
            }

            // Create new assignment
            var assignment = new PartnerRoleAssignment
            {
                Id = Guid.NewGuid(),
                PartnerId = partnerId,
                RoleId = role.Id,
                AssignedAt = DateTime.UtcNow,
                ExpiresAt = expiresAt,
                AssignmentReason = reason,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                CreatedBy = assignedBy,
                UpdatedBy = assignedBy,
                IsDeleted = false
            };

            _context.PartnerRoleAssignments.Add(assignment);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Role {RoleCode} assigned successfully to partner {PartnerId}", roleCode, partnerId);
            return Response<bool>.Success(true, "Role assigned successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role {RoleCode} to partner {PartnerId}", roleCode, partnerId);
            return Response<bool>.Failure<bool>("Error assigning role", "500");
        }
    }

    public async Task<Response<bool>> RemoveRoleAsync(
        Guid partnerId, 
        string roleCode, 
        Guid removedBy, 
        string reason, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Removing role {RoleCode} from partner {PartnerId} by {RemovedBy}", roleCode, partnerId, removedBy);
        
        try
        {
            // Find the role
            var role = await _context.PartnerRoles
                .Where(r => r.Code == roleCode && !r.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (role == null)
            {
                return Response<bool>.Failure<bool>($"Role {roleCode} not found", "404");
            }

            // Find active assignments
            var assignments = await _context.PartnerRoleAssignments
                .Where(pra => 
                    pra.PartnerId == partnerId && 
                    pra.RoleId == role.Id &&
                    pra.IsActive && 
                    !pra.IsDeleted)
                .ToListAsync(cancellationToken);

            if (!assignments.Any())
            {
                return Response<bool>.Success(true, "Role not assigned to partner");
            }

            // Deactivate assignments
            foreach (var assignment in assignments)
            {
                assignment.IsActive = false;
                assignment.IsDeleted = true;
                assignment.UpdatedAt = DateTime.UtcNow;
                assignment.UpdatedBy = removedBy;
                // Note: Could add removal reason to metadata if needed
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Role {RoleCode} removed successfully from partner {PartnerId}, {Count} assignments deactivated", 
                roleCode, partnerId, assignments.Count);
            
            return Response<bool>.Success(true, $"Role removed successfully ({assignments.Count} assignments deactivated)");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing role {RoleCode} from partner {PartnerId}", roleCode, partnerId);
            return Response<bool>.Failure<bool>("Error removing role", "500");
        }
    }

    public async Task<Response<bool>> HasRoleAsync(
        Guid partnerId, 
        string roleCode, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Checking if partner {PartnerId} has role {RoleCode}", partnerId, roleCode);
        
        try
        {
            var hasRole = await _context.PartnerRoleAssignments
                .Include(pra => pra.Role)
                .AnyAsync(pra => 
                    pra.PartnerId == partnerId && 
                    pra.Role.Code == roleCode &&
                    pra.IsActive && 
                    !pra.IsDeleted &&
                    pra.Role.IsActive &&
                    !pra.Role.IsDeleted &&
                    (pra.ExpiresAt == null || pra.ExpiresAt > DateTime.UtcNow), 
                cancellationToken);

            _logger.LogInformation("Partner {PartnerId} {HasRole} role {RoleCode}", 
                partnerId, hasRole ? "has" : "does not have", roleCode);
            
            return Response<bool>.Success(hasRole, hasRole ? "Partner has role" : "Partner does not have role");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking role {RoleCode} for partner {PartnerId}", roleCode, partnerId);
            return Response<bool>.Failure<bool>("Error checking role", "500");
        }
    }

    public async Task<Response<IEnumerable<PartnerRole>>> GetAllRolesAsync(
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting all roles");
        
        try
        {
            var roles = await _context.PartnerRoles
                .Where(r => r.IsActive && !r.IsDeleted)
                .OrderBy(r => r.Priority)
                .ThenBy(r => r.Name)
                .ToListAsync(cancellationToken);

            _logger.LogInformation("Retrieved {Count} active roles", roles.Count);
            
            return Response<IEnumerable<PartnerRole>>.Success<IEnumerable<PartnerRole>>(roles, $"Retrieved {roles.Count} roles");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all roles");
            return Response<IEnumerable<PartnerRole>>.Failure<IEnumerable<PartnerRole>>("Error retrieving roles", "500");
        }
    }

    public async Task<Response<PartnerRole?>> GetRoleByCodeAsync(
        string roleCode, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Getting role by code {RoleCode}", roleCode);
        
        try
        {
            var role = await _context.PartnerRoles
                .Where(r => r.Code == roleCode && r.IsActive && !r.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (role == null)
            {
                _logger.LogWarning("Role {RoleCode} not found", roleCode);
                return Response<PartnerRole?>.Success<PartnerRole?>(null, "Role not found");
            }

            _logger.LogInformation("Role {RoleCode} found: {RoleName}", roleCode, role.Name);
            return Response<PartnerRole?>.Success(role, "Role retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role by code {RoleCode}", roleCode);
            return Response<PartnerRole?>.Failure<PartnerRole?>("Error retrieving role", "500");
        }
    }

    public async Task<Response<PartnerRole>> CreateRoleAsync(
        string roleCode, 
        string name, 
        string description, 
        int priority, 
        int defaultApiRateLimit, 
        decimal defaultMonthlyInvoiceLimit, 
        Guid createdBy, 
        CancellationToken cancellationToken = default)
    {
        // TODO: Implement role creation
        _logger.LogInformation("Creating role {RoleCode}", roleCode);
        
        await Task.Delay(1, cancellationToken);
        
        var role = new PartnerRole
        {
            Id = Guid.NewGuid(),
            Code = roleCode,
            Name = name,
            Description = description,
            Priority = priority,
            DefaultApiRateLimitPerHour = defaultApiRateLimit,
            DefaultMonthlyInvoiceLimit = defaultMonthlyInvoiceLimit,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedBy = createdBy
        };
        
        return Response<PartnerRole>.Success(role, "Role created");
    }

    public async Task<Response<bool>> UpdateRoleAsync(
        Guid roleId, 
        string name, 
        string description, 
        int priority, 
        int defaultApiRateLimit, 
        decimal defaultMonthlyInvoiceLimit, 
        Guid updatedBy, 
        CancellationToken cancellationToken = default)
    {
        // TODO: Implement role update
        _logger.LogInformation("Updating role {RoleId}", roleId);
        
        await Task.Delay(1, cancellationToken);
        
        return Response<bool>.Success(true, "Role updated");
    }

    public async Task<Response<bool>> DeleteRoleAsync(
        Guid roleId, 
        Guid deletedBy, 
        string reason, 
        CancellationToken cancellationToken = default)
    {
        // TODO: Implement role deletion
        _logger.LogInformation("Deleting role {RoleId} by {DeletedBy}", roleId, deletedBy);
        
        await Task.Delay(1, cancellationToken);
        
        return Response<bool>.Success(true, "Role deleted");
    }

    public void ClearRoleCache(Guid partnerId)
    {
        // TODO: Implement role cache clearing
        _logger.LogInformation("Clearing role cache for partner {PartnerId}", partnerId);
    }
}