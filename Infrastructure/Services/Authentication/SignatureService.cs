using Applications.Interfaces.Services.Authentication;
using Microsoft.Extensions.Logging;
using Shared.Constants;
using Shared.Responses;
using System.Security.Cryptography;
using System.Text;
using Infrastructure.Persistences;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Services.Authentication;

/// <summary>
/// Implementation of HMAC signature generation and validation
/// </summary>
public class SignatureService(ILogger<SignatureService> logger, AppDbContext context) : ISignatureService
{
    private readonly ILogger<SignatureService> _logger = logger;
    private readonly AppDbContext _context = context;

    public string GenerateSignature(
        string httpMethod,
        string requestPath,
        string timestamp,
        string clientId,
        string payload,
        string hmacSecret)
    {
        _logger.LogInformation("Generating HMAC signature for {Method} {Path}", httpMethod, requestPath);

        try
        {
            // StringToSign format: HTTP_METHOD + "\n" + REQUEST_PATH + "\n" + TIMESTAMP + "\n" + CLIENT_ID + "\n" + PAYLOAD
            var stringToSign = $"{httpMethod}\n{requestPath}\n{timestamp}\n{clientId}\n{payload}";

            _logger.LogDebug("StringToSign: {StringToSign}", stringToSign);

            // Generate HMAC-SHA256 signature
            using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(hmacSecret));
            var hashBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(stringToSign));
            var signature = Convert.ToBase64String(hashBytes);

            _logger.LogInformation("Signature generated successfully");
            return signature;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating HMAC signature");
            throw;
        }
    }

    public async Task<Response<SignatureValidationResult>> ValidateSignatureAsync(
        Guid partnerId,
        string httpMethod,
        string requestPath,
        string timestamp,
        string clientId,
        string payload,
        string providedSignature,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Validating HMAC signature for partner {PartnerId}", partnerId);

        try
        {
            // Get partner's HMAC secret from database
            var partner = await _context.Partners
                .Where(p => p.Id == partnerId && !p.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (partner == null)
            {
                _logger.LogWarning("Partner {PartnerId} not found for signature validation", partnerId);
                return Response<SignatureValidationResult>.Failure<SignatureValidationResult>(
                    "Partner not found", ErrorCodes.NOT_FOUND_DATA);
            }

            // Use the partner's actual HMAC secret for signature validation
            // TODO: Implement proper encryption for HMAC secrets in production
            // For now, HmacSecretHash stores the plaintext HMAC secret (not BCrypt hash)
            var hmacSecret = partner.HmacSecretHash; // Use partner's actual HMAC secret (plaintext)

            // Generate expected signature
            var expectedSignature = GenerateSignature(httpMethod, requestPath, timestamp, clientId, payload, hmacSecret);

            // Compare signatures
            var isValid = string.Equals(expectedSignature, providedSignature, StringComparison.Ordinal);

            if (isValid)
            {
                _logger.LogInformation("Signature validation successful for partner {PartnerId}", partnerId);
            }
            else
            {
                _logger.LogWarning("Signature validation failed for partner {PartnerId}. Expected: {Expected}, Provided: {Provided}",
                    partnerId, expectedSignature, providedSignature);
            }

            return Response<SignatureValidationResult>.Success(new SignatureValidationResult
            {
                IsValid = isValid,
                RequestTime = DateTime.UtcNow,
                ExpectedSignature = expectedSignature,
                ProvidedSignature = providedSignature
            }, isValid ? "Signature validation successful" : "Signature validation failed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating signature for partner {PartnerId}", partnerId);
            return Response<SignatureValidationResult>.Failure<SignatureValidationResult>(
                "Signature validation error", ErrorCodes.INTERNAL_SERVER_ERROR);
        }
    }

    public Response<bool> ValidateTimestamp(string timestamp, int toleranceSeconds = 300)
    {
        // TODO: Implement timestamp validation
        _logger.LogInformation("Validating timestamp {Timestamp}", timestamp);

        if (!long.TryParse(timestamp, out var unixTime))
        {
            return Response<bool>.Failure<bool>("Invalid timestamp format", ErrorCodes.BAD_REQUEST_ERROR);
        }

        var requestTime = DateTimeOffset.FromUnixTimeSeconds(unixTime).UtcDateTime;
        var now = DateTime.UtcNow;
        var timeDiff = Math.Abs((now - requestTime).TotalSeconds);

        if (timeDiff > toleranceSeconds)
        {
            return Response<bool>.Failure<bool>($"Timestamp too old. Difference: {timeDiff}s, Tolerance: {toleranceSeconds}s", ErrorCodes.BAD_REQUEST_ERROR);
        }

        return Response<bool>.Success(true);
    }

    public string GetCurrentTimestamp()
    {
        return DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString();
    }

    public DateTime? ParseTimestamp(string timestamp)
    {
        if (!long.TryParse(timestamp, out var unixTime))
        {
            return null;
        }

        return DateTimeOffset.FromUnixTimeSeconds(unixTime).UtcDateTime;
    }
}