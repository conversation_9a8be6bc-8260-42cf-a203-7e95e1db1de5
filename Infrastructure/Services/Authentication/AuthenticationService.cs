using Applications.Interfaces.Services.Authentication;
using Core.Entities.Authentication;
using Infrastructure.Persistences;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Shared.Responses;
using BCrypt.Net;

namespace Infrastructure.Services.Authentication;

/// <summary>
/// Implementation of OAuth2 client credentials authentication
/// </summary>
public class AuthenticationService : IAuthenticationService
{
    private readonly AppDbContext _context;
    private readonly ITokenService _tokenService;
    private readonly ILogger<AuthenticationService> _logger;

    public AuthenticationService(
        AppDbContext context,
        ITokenService tokenService,
        ILogger<AuthenticationService> logger)
    {
        _context = context;
        _tokenService = tokenService;
        _logger = logger;
    }

    public async Task<Response<AuthenticationResult>> AuthenticateAsync(
        string clientId, 
        string clientSecret, 
        string ipAddress, 
        string? userAgent = null, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Authentication attempt for client {ClientId} from {IpAddress}", clientId, ipAddress);
        
        try
        {
            // 1. Find partner by ClientId
            var partner = await _context.Partners
                .Where(p => p.ClientId == clientId && !p.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (partner == null)
            {
                _logger.LogWarning("Partner not found for ClientId {ClientId}", clientId);
                return Response<AuthenticationResult>.Failure<AuthenticationResult>("Invalid credentials", "401");
            }

            // 2. Check if partner is active
            if (!partner.IsActive)
            {
                _logger.LogWarning("Partner {ClientId} is not active", clientId);
                return Response<AuthenticationResult>.Failure<AuthenticationResult>("Account disabled", "401");
            }

            // 3. Verify client secret
            if (!BCrypt.Net.BCrypt.Verify(clientSecret, partner.ClientSecretHash))
            {
                _logger.LogWarning("Invalid client secret for {ClientId}", clientId);
                return Response<AuthenticationResult>.Failure<AuthenticationResult>("Invalid credentials", "401");
            }

            // 4. Get partner permissions/scopes
            var scopes = await GetPartnerScopesAsync(partner.Id, cancellationToken);

            // 5. Generate token
            var tokenInfo = await _tokenService.GenerateTokenAsync(partner, scopes, ipAddress, userAgent);

            // 6. Log successful authentication
            await LogAuthenticationAsync(partner.Id, clientId, ipAddress, userAgent, true, null, cancellationToken);

            // 7. Return authentication result
            var result = new AuthenticationResult
            {
                AccessToken = tokenInfo.AccessToken,
                TokenType = "Bearer",
                ExpiresIn = tokenInfo.ExpiresIn,
                Scopes = scopes,
                PartnerId = partner.Id,
                PartnerName = partner.Name
            };

            _logger.LogInformation("Authentication successful for client {ClientId}", clientId);
            return Response<AuthenticationResult>.Success(result, "Authentication successful");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during authentication for client {ClientId}", clientId);
            return Response<AuthenticationResult>.Failure<AuthenticationResult>("Internal server error", "500");
        }
    }

    public async Task<Response<TokenValidationResult>> ValidateTokenAsync(
        string accessToken, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Validating access token via AuthenticationService");
        
        try
        {
            // Delegate to TokenService for actual validation
            var tokenValidation = await _tokenService.ValidateTokenAsync(accessToken);
            
            if (!tokenValidation.IsValid)
            {
                return Response<TokenValidationResult>.Failure<TokenValidationResult>(
                    tokenValidation.ErrorMessage ?? "Token validation failed", "401");
            }

            return Response<TokenValidationResult>.Success(tokenValidation, "Token validation successful");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating token via AuthenticationService");
            return Response<TokenValidationResult>.Failure<TokenValidationResult>(
                "Token validation error", "500");
        }
    }

    public async Task<Response<bool>> RevokeTokenAsync(
        string accessToken, 
        string reason, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Revoking access token: {Reason}", reason);
        
        try
        {
            // Find and revoke token in database
            var partnerToken = await _context.PartnerTokens
                .Where(pt => pt.AccessToken == accessToken && pt.IsActive && !pt.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (partnerToken == null)
            {
                return Response<bool>.Failure<bool>("Token not found", "404");
            }

            // Revoke token
            partnerToken.IsActive = false;
            partnerToken.RevokedAt = DateTime.UtcNow;
            partnerToken.RevocationReason = reason;
            
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Token revoked successfully for partner {PartnerId}", partnerToken.PartnerId);
            return Response<bool>.Success(true, "Token revoked successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking token");
            return Response<bool>.Failure<bool>("Token revocation error", "500");
        }
    }

    public async Task<Response<bool>> IsIpWhitelistedAsync(
        Guid partnerId, 
        string ipAddress, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("IP whitelist check for partner {PartnerId}, IP {IpAddress}", partnerId, ipAddress);
        
        try
        {
            // Get partner from database to check IP whitelist configuration
            var partner = await _context.Partners
                .Where(p => p.Id == partnerId && !p.IsDeleted)
                .FirstOrDefaultAsync(cancellationToken);

            if (partner == null)
            {
                _logger.LogWarning("Partner {PartnerId} not found for IP whitelist check", partnerId);
                return Response<bool>.Failure<bool>("Partner not found", "404");
            }

            // If IP whitelist is disabled for this partner, allow all IPs
            if (!partner.EnableIpWhitelist)
            {
                _logger.LogInformation("IP whitelist disabled for partner {PartnerId} - allowing IP {IpAddress}", 
                    partnerId, ipAddress);
                return Response<bool>.Success(true, "IP whitelist disabled for partner");
            }

            // Parse and validate IP whitelist
            if (string.IsNullOrWhiteSpace(partner.IpWhitelist))
            {
                _logger.LogWarning("Partner {PartnerId} has IP whitelist enabled but empty whitelist - blocking all IPs", 
                    partnerId);
                return Response<bool>.Success(false, "Empty IP whitelist - access denied");
            }

            // Use simplified IP matching for AuthenticationService
            var whitelistEntries = System.Text.Json.JsonSerializer.Deserialize<string[]>(partner.IpWhitelist) ?? Array.Empty<string>();
            
            foreach (var entry in whitelistEntries)
            {
                if (string.IsNullOrWhiteSpace(entry)) continue;
                
                var trimmedEntry = entry.Trim();
                
                // Simple exact match or localhost patterns
                if (string.Equals(ipAddress, trimmedEntry, StringComparison.OrdinalIgnoreCase) ||
                    (trimmedEntry == "localhost" && (ipAddress == "127.0.0.1" || ipAddress == "::1")) ||
                    (trimmedEntry == "0.0.0.0" && ipAddress == "127.0.0.1"))
                {
                    _logger.LogInformation("IP {IpAddress} allowed for partner {PartnerId} - matched entry: {Entry}", 
                        ipAddress, partnerId, trimmedEntry);
                    return Response<bool>.Success(true, $"IP allowed - matched: {trimmedEntry}");
                }
            }

            _logger.LogWarning("IP {IpAddress} not in whitelist for partner {PartnerId}", ipAddress, partnerId);
            return Response<bool>.Success(false, "IP address not in whitelist");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking IP whitelist for partner {PartnerId}, IP {IpAddress}", 
                partnerId, ipAddress);
            return Response<bool>.Failure<bool>("IP whitelist check error", "500");
        }
    }

    private async Task<string[]> GetPartnerScopesAsync(Guid partnerId, CancellationToken cancellationToken)
    {
        try
        {
            var scopes = await _context.RoleFunctionPermissions
                .Include(rfp => rfp.Function)
                .Include(rfp => rfp.Permission)
                .Where(rfp => rfp.Role.PartnerRoleAssignments.Any(pra => 
                    pra.PartnerId == partnerId && 
                    pra.IsActive && 
                    !pra.IsDeleted &&
                    (pra.ExpiresAt == null || pra.ExpiresAt > DateTime.UtcNow)) &&
                    rfp.IsGranted && 
                    !rfp.IsDeleted)
                .Select(rfp => rfp.Function.Code.ToLower() + "_" + rfp.Permission.Code.ToLower())
                .Distinct()
                .ToArrayAsync(cancellationToken);

            return scopes;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting scopes for partner {PartnerId}", partnerId);
            return Array.Empty<string>();
        }
    }

    private async Task LogAuthenticationAsync(
        Guid? partnerId, 
        string? clientId, 
        string ipAddress, 
        string? userAgent, 
        bool success, 
        string? failureReason, 
        CancellationToken cancellationToken)
    {
        try
        {
            var log = new AuthenticationLog
            {
                Id = Guid.NewGuid(),
                PartnerId = partnerId,
                ClientId = clientId,
                IpAddress = ipAddress,
                UserAgent = userAgent,
                Success = success,
                FailureReason = failureReason,
                AuthenticationMethod = "OAuth2",
                Timestamp = DateTime.UtcNow,
                RequestPath = "/api/authentication/token",
                HttpMethod = "POST",
                Metadata = $"{{\"timestamp\": \"{DateTime.UtcNow:O}\", \"success\": {success.ToString().ToLower()}}}",
                CreatedAt = DateTime.UtcNow,
                CreatedBy = partnerId,
                IsDeleted = false
            };

            _context.AuthenticationLogs.Add(log);
            await _context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging authentication attempt");
            // Don't throw here - logging failure shouldn't break authentication
        }
    }
}