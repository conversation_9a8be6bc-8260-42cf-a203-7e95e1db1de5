# 🔒 HƯỚNG DẪN FULL SECURITY AUTHENTICATION/AUTHORIZATION

## 📋 Tổng quan

Hệ thống đã được cấu hình với **FULL SECURITY** bao gồm 5 middleware layers:

```
Request → IP Whitelist → JWT Auth → HMAC Signature → Permissions → Usage Tracking → API
```

## 🎯 Cấu hình hiện tại

### ✅ Security Options Enabled:
- **IP Whitelist**: `EnableIpWhitelist = true`
- **Timestamp Validation**: `EnableTimestampValidation = true` 
- **Usage Tracking**: `EnableUsageTracking = true`
- **HMAC Signature**: `DefaultHashAlgorithm = "HMAC-SHA256"`
- **Signature Tolerance**: `300 seconds` (5 phút)

### 🏢 ZenShop Configuration:
- **Client ID**: `zenshop_client`
- **Password**: `password`
- **IP Whitelist**: `["127.0.0.1", "::1", "localhost", "0.0.0.0"]`
- **Permissions**: `MOBIFONE_API_EXECUTE`, `INVOICE_CREATE`, etc.

## 🚀 STEP BY STEP - Cách test Full Security

### Bước 1: Chuẩn bị
```bash
# 1. Restart API server để áp dụng cấu hình mới
# 2. Seed database với IP whitelist
# 3. Chạy test script
./test_full_security.sh
```

### Bước 2: Manual Testing

#### 2.1. Tạo HMAC Signature
```bash
# Function tính HMAC signature
create_signature() {
    local method="$1"      # POST, GET, PUT, DELETE
    local path="$2"        # /api/mobifone-invoice/login
    local timestamp="$3"   # Unix timestamp
    local client_id="$4"   # zenshop_client
    local payload="$5"     # JSON body (empty cho GET)
    local secret="password" # HMAC secret
    
    # StringToSign format
    local string_to_sign="${method}\n${path}\n${timestamp}\n${client_id}\n${payload}"
    
    # Generate HMAC-SHA256 signature
    echo -n "$string_to_sign" | openssl dgst -sha256 -hmac "$secret" -binary | base64
}
```

#### 2.2. Authentication Flow
```bash
# 1. Get current timestamp
TIMESTAMP=$(date +%s)

# 2. Prepare request data
CLIENT_ID="zenshop_client"
PAYLOAD='{"clientId":"zenshop_client","clientSecret":"password","grantType":"client_credentials"}'

# 3. Calculate signature
SIGNATURE=$(create_signature "POST" "/api/authentication/token" "$TIMESTAMP" "$CLIENT_ID" "$PAYLOAD")

# 4. Call authentication API
curl -X POST "https://localhost:5001/api/authentication/token" \
  -H "Content-Type: application/json" \
  -H "X-Client-Id: zenshop_client" \
  -H "X-Timestamp: $TIMESTAMP" \
  -H "X-Signature: $SIGNATURE" \
  -d "$PAYLOAD" \
  -k
```

#### 2.3. API Call với Full Security
```bash
# 1. Extract token from authentication response
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# 2. Prepare API request
API_TIMESTAMP=$(date +%s)
API_PAYLOAD='{"username":"test","password":"test"}'
API_SIGNATURE=$(create_signature "POST" "/api/mobifone-invoice/login" "$API_TIMESTAMP" "zenshop_client" "$API_PAYLOAD")

# 3. Call protected API
curl -X POST "https://localhost:5001/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Client-Id: zenshop_client" \
  -H "X-Timestamp: $API_TIMESTAMP" \
  -H "X-Signature: $API_SIGNATURE" \
  -H "Content-Type: application/json" \
  -d "$API_PAYLOAD" \
  -k
```

## 🔍 Middleware Pipeline Chi tiết

### 1. 🌐 IpWhitelistMiddleware
**Chức năng**: Kiểm tra IP của client có trong whitelist không

**Flow**:
```
Request IP → Check Partner.IpWhitelist → Allow/Deny
```

**Test**:
- ✅ `127.0.0.1`, `localhost` → Should pass
- ❌ `*************` → Should be denied

### 2. 🔑 JwtAuthenticationMiddleware  
**Chức năng**: Validate JWT token và extract claims

**Flow**:
```
Authorization Header → Parse JWT → Validate → Extract Claims → Set User Context
```

**Test**:
- ✅ Valid JWT → Should pass
- ❌ Invalid/Expired JWT → Should be denied

### 3. 🔏 SignatureValidationMiddleware
**Chức năng**: Validate HMAC-SHA256 signature

**Flow**:
```
Headers → Reconstruct StringToSign → Calculate HMAC → Compare Signatures
```

**StringToSign Format**:
```
POST\n/api/mobifone-invoice/login\n1625097600\nzenshop_client\n{"username":"test","password":"test"}
```

**Test**:
- ✅ Correct signature → Should pass
- ❌ Wrong signature → Should be denied
- ❌ Expired timestamp → Should be denied

### 4. 🛡️ PermissionAuthorizationMiddleware
**Chức năng**: Kiểm tra permission theo `[RequirePermission]` attribute

**Flow**:
```
Controller Action → Check [RequirePermission] → Validate User Permissions → Allow/Deny
```

**Test**:
- ✅ `MOBIFONE_API_EXECUTE` → ZenShop should have access
- ❌ `ADMIN_MANAGE` → ZenShop should be denied

### 5. 📊 UsageTrackingMiddleware
**Chức năng**: Log API usage cho rate limiting

**Flow**:
```
API Call → Log Usage → Check Rate Limits → Continue/Throttle
```

## 🧪 Test Cases

### ✅ Positive Tests
1. **Valid full flow**: IP + JWT + Signature + Permission → Success
2. **Multiple API calls**: Usage tracking working
3. **Different endpoints**: Permission checking working

### ❌ Negative Tests  
1. **Invalid IP**: Should be denied at IP whitelist
2. **Invalid JWT**: Should be denied at JWT validation
3. **Invalid signature**: Should be denied at signature validation
4. **Wrong timestamp**: Should be denied (>5 min old)
5. **Missing permission**: Should be denied at authorization
6. **Rate limit exceeded**: Should be throttled

## 🎯 Expected Results

### Authentication Success:
```json
{
  "code": "000",
  "message": "Token issued successfully", 
  "data": {
    "accessToken": "eyJ...",
    "tokenType": "Bearer",
    "expiresIn": 7200,
    "scope": "invoice_create_create mobifone_api_execute ..."
  },
  "isSuccess": true
}
```

### API Call Success:
```json
{
  "Code": "500", // or "200" 
  "Message": "Not implemented yet", // or actual response
  "Data": null,
  "IsSuccess": false // or true
}
```

**Note**: Code 500 = Business logic chưa implement, nhưng security đã PASS!

### Security Denial:
```json
{
  "Code": "401",
  "Message": "Unauthorized",
  "Data": null,
  "IsSuccess": false
}
```

## 🔧 Troubleshooting

### 401 Unauthorized:
- ❌ IP không trong whitelist
- ❌ JWT token invalid/expired  
- ❌ HMAC signature sai
- ❌ Timestamp quá cũ (>5 phút)

### 403 Forbidden:
- ❌ Không có permission cần thiết
- ❌ Rate limit exceeded

### 500 Server Error:
- ✅ Security đã pass, chỉ là business logic chưa implement

## 🚀 Next Steps

1. **Run test**: `./test_full_security.sh`
2. **Check logs**: Xem middleware logs trong console
3. **Verify database**: Check AuthenticationLogs, UsageLogs tables
4. **Test Swagger**: Thử từ Swagger UI với proper headers
5. **Production**: Deploy với real IP whitelist và secrets

---

💡 **Tip**: Sử dụng script test để verify toàn bộ security pipeline hoạt động đúng!