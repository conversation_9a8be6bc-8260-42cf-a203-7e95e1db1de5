# 📋 Hướng dẫn Test Luồng Authentication/Authorization - Chi Tiết

## 🎯 Tổng quan

Hướng dẫn này sẽ gi<PERSON><PERSON> bạn test đầy đủ luồng authentication/authorization của ZenInvoice API, bao gồm:
- OAuth2 Client Credentials authentication
- JWT token validation  
- IP whitelist checking
- HMAC signature validation
- Permission-based authorization
- Rate limiting và usage tracking

## 🔧 **Bước 1: Chuẩn bị dữ liệu Database**

### 1.1 Import Seed Data
```sql
-- Chạy script seed data (đã có sẵn)
psql -U postgres -d zen_invoice_dev -f Scripts/CorrectSeedData_Fixed.sql
```

### 1.2 Kiểm tra dữ liệu test có sẵn
```sql
-- <PERSON><PERSON><PERSON> tra Partners
SELECT "ClientId", "Name", "EnableIpWhitelist", "IsActive" 
FROM "Partners" 
WHERE "ClientId" IN ('zenshop_client', 'admin_client_001');

-- <PERSON><PERSON><PERSON> tra Roles
SELECT "Code", "Name", "Priority" FROM "PartnerRoles";

-- <PERSON><PERSON><PERSON> tra Permissions  
SELECT "Code", "Name" FROM "Permissions";
```

**Kết quả mong đợi:**
- `zenshop_client`: ZenShop System, IP whitelist disabled, Active
- `admin_client_001`: Administrator, IP whitelist disabled, Active

## 🚀 **Bước 2: Khởi động API Server**

```bash
# Trong thư mục project root
dotnet run --project API

# Server sẽ chạy tại: http://localhost:5119
# Logs: Theo dõi console để xem authentication flow
```

**Kiểm tra server đã khởi động:**
```bash
curl -X GET "http://localhost:5119/health" || echo "Server chưa sẵn sàng"
```

## 🔐 **Bước 3: Test Authentication Flow**

### 3.1 **Test Case 1: Lấy Access Token** ✅

**Request:**
```bash
curl -X POST "http://localhost:5119/api/authentication/token" \
  -H "Content-Type: application/json" \
  -H "X-Client-ID: zenshop_client" \
  -d '{
    "ClientId": "zenshop_client",
    "ClientSecret": "password", 
    "GrantType": "client_credentials",
    "Scope": "invoice_api"
  }'
```

**Expected Response:**
```json
{
  "code": "000",
  "message": "Token issued successfully", 
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "tokenType": "Bearer",
    "expiresIn": 7200,
    "scope": "invoice_create_create invoice_create_execute..."
  },
  "isSuccess": true
}
```

**Logs để theo dõi trong console:**
```
[INFO] Token request from client zenshop_client, IP ::1
[INFO] Authentication attempt for client zenshop_client from ::1  
[INFO] Generating token for partner 55555555-5555-5555-5555-555555555552
[INFO] Authentication successful for client zenshop_client
[INFO] Token issued successfully for client zenshop_client
```

### 3.2 **Test Case 2: Validate Token** ✅

```bash
# Lưu token từ response trước
ACCESS_TOKEN="eyJhbGciOiJIUzI1NiIs..."

curl -X POST "http://localhost:5119/api/authentication/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "AccessToken": "'$ACCESS_TOKEN'"
  }'
```

**Lưu ý:** API này không cần Authorization header vì mục đích là để validate token, không phải authenticate.

**Expected Response:**
```json
{
  "code": "000",
  "message": "Token is valid",
  "data": {
    "isValid": true,
    "partnerId": "55555555-5555-5555-5555-555555555552",
    "partnerName": "ZenShop System",
    "expiresAt": "2025-07-06T17:43:29Z",
    "scopes": ["invoice_api"]
  },
  "isSuccess": true
}
```

## 🛡️ **Bước 4: Test Authorization Flow (Với đầy đủ middleware)**

### 4.1 **Chuẩn bị Signature HMAC-SHA256** 🔐

```bash
# Set variables (Lưu token từ bước 3.1)
ACCESS_TOKEN="eyJhbGciOiJIUzI1NiIs..."
TIMESTAMP=$(date +%s)
PAYLOAD='{"Username":"<EMAIL>","Password":"Mobifone@2024","Environment":"Test"}'

# Generate signature string theo format: METHOD\nPATH\nTIMESTAMP\nCLIENT_ID\nPAYLOAD
STRING_TO_SIGN="POST\n/zenInvoice/api/mobi-fone-invoice/login\n${TIMESTAMP}\nzenshop_client\n${PAYLOAD}"

# Generate HMAC-SHA256 signature với secret "password"
SIGNATURE=$(echo -n "$STRING_TO_SIGN" | openssl dgst -sha256 -hmac "password" -binary | base64)

echo "Timestamp: $TIMESTAMP"
echo "Generated Signature: $SIGNATURE"
```

### 4.2 **Test Case 3: Full Authorization Pipeline** 🚀

**Request với tất cả headers bắt buộc:**
```bash
curl -X POST "http://localhost:5119/zenInvoice/api/mobi-fone-invoice/login" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "X-Client-ID: zenshop_client" \
  -H "X-Timestamp: $TIMESTAMP" \
  -H "X-Signature: $SIGNATURE" \
  -d "$PAYLOAD"
```

**Expected Response:**
```json
{
  "code": "000",
  "message": "",
  "data": {
    "token": "",
    "ma_dvcs": "", 
    "wb_user_id": ""
  },
  "isSuccess": true
}
```

**Logs đầy đủ pipeline trong console:**
```
[INFO] IP validation prepared for ::1 on /zenInvoice/api/mobi-fone-invoice/login
[INFO] Validating JWT token
[INFO] Token validated successfully for partner 55555555-5555-5555-5555-555555555552
[INFO] Extracting claims from JWT token  
[INFO] Claims extracted successfully for partner 55555555-5555-5555-555555555552
[INFO] Partner ID extracted successfully: 55555555-5555-5555-5555-555555555552
[INFO] Checking IP whitelist for partner 55555555-5555-5555-5555-555555555552, IP ::1
[INFO] IP whitelist disabled for partner 55555555-5555-5555-5555-555555555552
[INFO] Partner 55555555-5555-5555-5555-555555555552 authenticated successfully
[INFO] Validating timestamp 1751791409
[INFO] Validating HMAC signature for partner 55555555-5555-5555-5555-555555555552
[INFO] Generating HMAC signature for POST /zenInvoice/api/mobi-fone-invoice/login
[INFO] Signature generated successfully
[INFO] Signature validation successful for partner 55555555-5555-5555-5555-555555555552
[INFO] Signature validated successfully for partner 55555555-5555-5555-5555-555555555552
[INFO] Checking permission for partner 55555555-5555-5555-5555-555555555552, function MOBIFONE_API, permission EXECUTE
[INFO] Permission granted for partner 55555555-5555-5555-5555-555555555552: MOBIFONE_API.EXECUTE via roles: PREMIUM_PARTNER
[INFO] MobiFone Login API call completed successfully
[INFO] Tracking API call for partner 55555555-5555-5555-5555-555555555552, operation invoice_create, success True
```

**✅ Nếu thấy logs trên = THÀNH CÔNG! Tất cả middleware đã hoạt động.**

## 🧪 **Bước 5: Test Error Scenarios**

### 5.1 **Test Case 4: Invalid Credentials** ❌

```bash
curl -X POST "http://localhost:5119/api/authentication/token" \
  -H "Content-Type: application/json" \
  -H "X-Client-ID: zenshop_client" \
  -d '{
    "ClientId": "zenshop_client",
    "ClientSecret": "wrong_password",
    "GrantType": "client_credentials"
  }'
```

**Expected Response:**
```json
{
  "code": "401",
  "message": "Authentication failed",
  "isSuccess": false
}
```

**Logs:**
```
[WRN] Invalid client secret for zenshop_client
[WRN] Authentication failed for client zenshop_client, IP ::1: Invalid credentials
```

### 5.2 **Test Case 5: Invalid Signature** ❌

```bash
curl -X POST "http://localhost:5119/zenInvoice/api/mobi-fone-invoice/login" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "X-Client-ID: zenshop_client" \
  -H "X-Timestamp: $TIMESTAMP" \
  -H "X-Signature: invalid_signature_here" \
  -d "$PAYLOAD"
```

**Expected Response:**
```json
{
  "Code": "401",
  "Message": "Signature validation failed",
  "IsSuccess": false
}
```

**Logs:**
```
[WRN] Signature validation failed for partner 555...552. Expected: abc123=, Provided: invalid_signature_here
[WRN] Invalid signature from partner 555...552, client zenshop_client
```

### 5.3 **Test Case 6: Missing Authorization Header** ❌

```bash
curl -X POST "http://localhost:5119/zenInvoice/api/mobi-fone-invoice/login" \
  -H "Content-Type: application/json" \
  -H "X-Client-ID: zenshop_client" \
  -H "X-Timestamp: $TIMESTAMP" \
  -H "X-Signature: $SIGNATURE" \
  -d "$PAYLOAD"
```

**Expected:** `401 Unauthorized` với message "Missing or invalid Authorization header"

### 5.4 **Test Case 7: Expired Timestamp** ❌

```bash
# Sử dụng timestamp cũ (> 5 phút)
OLD_TIMESTAMP=$(($(date +%s) - 600))

# Generate signature với timestamp cũ
OLD_STRING_TO_SIGN="POST\n/zenInvoice/api/mobi-fone-invoice/login\n${OLD_TIMESTAMP}\nzenshop_client\n${PAYLOAD}"
OLD_SIGNATURE=$(echo -n "$OLD_STRING_TO_SIGN" | openssl dgst -sha256 -hmac "password" -binary | base64)

curl -X POST "http://localhost:5119/zenInvoice/api/mobi-fone-invoice/login" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "X-Client-ID: zenshop_client" \
  -H "X-Timestamp: $OLD_TIMESTAMP" \
  -H "X-Signature: $OLD_SIGNATURE" \
  -d "$PAYLOAD"
```

**Expected:** `401 Unauthorized` với message "Request timestamp too old"

## 📊 **Bước 6: Test IP Whitelist (Tùy chọn)**

### 6.1 **Bật IP Whitelist cho ZenShop**
```sql
UPDATE "Partners" 
SET "EnableIpWhitelist" = true 
WHERE "ClientId" = 'zenshop_client';
```

### 6.2 **Test từ IP được phép**
Chạy lại Test Case 3 - sẽ PASS vì `::1` (localhost) có trong whitelist.

### 6.3 **Simulate test từ IP không được phép**
```sql
-- Xóa localhost khỏi whitelist để test
UPDATE "Partners" 
SET "IpWhitelist" = '["*************"]'
WHERE "ClientId" = 'zenshop_client';
```

Chạy lại Test Case 3 - sẽ bị reject với:
```json
{
  "Code": "403",
  "Message": "IP address not whitelisted",
  "IsSuccess": false
}
```

### 6.4 **Khôi phục IP Whitelist**
```sql
UPDATE "Partners" 
SET "EnableIpWhitelist" = false,
    "IpWhitelist" = '["127.0.0.1", "::1", "localhost", "0.0.0.0"]'
WHERE "ClientId" = 'zenshop_client';
```

## 🎯 **Credentials Summary cho Testing**

| Partner | ClientId | ClientSecret | HMAC Secret | Roles | Permissions |
|---------|----------|-------------|-------------|-------|-------------|
| **ZenShop** | `zenshop_client` | `password` | `password` | PREMIUM_PARTNER | MOBIFONE_API.EXECUTE, INVOICE.* |
| **Admin** | `admin_client_001` | `password` | `password` | ADMIN | ALL |
| **MobiFone** | `mobifone_client` | `password` | `password` | PREMIUM_PARTNER | MOBIFONE_API.EXECUTE |
| **Test** | `test_client` | `password` | `password` | BASIC_PARTNER | INVOICE.READ |

## 🔧 **Tools hỗ trợ testing**

### Auto Test Script

Tạo file `auto_test.sh`:
```bash
#!/bin/bash
# auto_test.sh - Automated Authentication Testing

echo "🚀 === Testing ZenInvoice Authentication Flow ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test 1: Get Token
echo -e "\n${YELLOW}📝 Test 1: Getting Access Token...${NC}"
TOKEN_RESPONSE=$(curl -s -X POST "http://localhost:5119/api/authentication/token" \
  -H "Content-Type: application/json" \
  -H "X-Client-ID: zenshop_client" \
  -d '{"ClientId":"zenshop_client","ClientSecret":"password","GrantType":"client_credentials","Scope":"invoice_api"}')

if echo "$TOKEN_RESPONSE" | grep -q '"isSuccess":true'; then
    ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)
    echo -e "${GREEN}✅ Token obtained: ${ACCESS_TOKEN:0:20}...${NC}"
else
    echo -e "${RED}❌ Failed to get token${NC}"
    echo "Response: $TOKEN_RESPONSE"
    exit 1
fi

# Test 2: Generate Signature
echo -e "\n${YELLOW}🔐 Test 2: Generating HMAC Signature...${NC}"
TIMESTAMP=$(date +%s)
PAYLOAD='{"Username":"<EMAIL>","Password":"Mobifone@2024","Environment":"Test"}'
STRING_TO_SIGN="POST\n/zenInvoice/api/mobi-fone-invoice/login\n${TIMESTAMP}\nzenshop_client\n${PAYLOAD}"
SIGNATURE=$(echo -n "$STRING_TO_SIGN" | openssl dgst -sha256 -hmac "password" -binary | base64)
echo -e "${GREEN}✅ Signature generated: ${SIGNATURE:0:20}...${NC}"

# Test 3: Full API Call
echo -e "\n${YELLOW}🛡️ Test 3: Full Authorization Pipeline...${NC}"
API_RESPONSE=$(curl -s -X POST "http://localhost:5119/zenInvoice/api/mobi-fone-invoice/login" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "X-Client-ID: zenshop_client" \
  -H "X-Timestamp: $TIMESTAMP" \
  -H "X-Signature: $SIGNATURE" \
  -d "$PAYLOAD")

if echo "$API_RESPONSE" | grep -q '"isSuccess":true'; then
    echo -e "${GREEN}✅ API Call Successful!${NC}"
    echo "Response: $API_RESPONSE"
else
    echo -e "${RED}❌ API Call Failed${NC}"
    echo "Response: $API_RESPONSE"
fi

# Test 4: Invalid Credentials
echo -e "\n${YELLOW}🚫 Test 4: Testing Invalid Credentials...${NC}"
INVALID_RESPONSE=$(curl -s -X POST "http://localhost:5119/api/authentication/token" \
  -H "Content-Type: application/json" \
  -H "X-Client-ID: zenshop_client" \
  -d '{"ClientId":"zenshop_client","ClientSecret":"wrong_password","GrantType":"client_credentials"}')

if echo "$INVALID_RESPONSE" | grep -q '"isSuccess":false'; then
    echo -e "${GREEN}✅ Invalid credentials properly rejected${NC}"
else
    echo -e "${RED}❌ Security issue: Invalid credentials accepted${NC}"
fi

echo -e "\n${GREEN}🎉 Testing completed!${NC}"
echo -e "\n${YELLOW}📋 Summary:${NC}"
echo "- ✅ OAuth2 Token Generation"
echo "- ✅ HMAC Signature Generation" 
echo "- ✅ Full Authorization Pipeline"
echo "- ✅ Security Validation"
```

**Chạy script:**
```bash
chmod +x auto_test.sh
./auto_test.sh
```

### Manual Testing với curl và jq

```bash
# Lấy và parse token
TOKEN=$(curl -s -X POST "http://localhost:5119/api/authentication/token" \
  -H "Content-Type: application/json" \
  -H "X-Client-ID: zenshop_client" \
  -d '{"ClientId":"zenshop_client","ClientSecret":"password","GrantType":"client_credentials","Scope":"invoice_api"}' \
  | jq -r '.data.accessToken')

echo "Token: $TOKEN"

# Test validate token
curl -s -X POST "http://localhost:5119/api/authentication/validate" \
  -H "Content-Type: application/json" \
  -d "{\"AccessToken\":\"$TOKEN\"}" | jq .
```

## 🚨 **Troubleshooting**

### Lỗi thường gặp:

1. **"Server not responding"**
   - Kiểm tra: `dotnet run --project API` đã chạy chưa
   - Port 5119 có bị conflict không

2. **"Invalid client secret"**
   - Đảm bảo sử dụng password `"password"` (không phải `"zenshop_secret_123"`)

3. **"Signature validation failed"**
   - Kiểm tra format STRING_TO_SIGN có đúng `\n` newlines
   - HMAC secret phải là `"password"`
   - Base64 encoding phải chính xác

4. **"Token expired"**
   - Token có thời hạn 2 tiờ, lấy token mới nếu cần

5. **"Permission denied"**
   - Kiểm tra partner có role PREMIUM_PARTNER không
   - Function MOBIFONE_API có permission EXECUTE không

### Logs debugging:

```bash
# Theo dõi logs real-time
dotnet run --project API | grep -E "(INFO|WARN|ERROR)"

# Chỉ xem authentication logs  
dotnet run --project API | grep -E "(Authentication|Token|Signature|Permission)"
```

## 📈 **Next Steps**

Sau khi test thành công basic flow, có thể test thêm:

1. **Rate Limiting**: Gửi nhiều request liên tục để test rate limit
2. **Multiple Partners**: Test với `admin_client_001`, `test_client` 
3. **Different APIs**: Test với các API khác ngoài MobiFone login
4. **Load Testing**: Sử dụng Apache Bench hoặc wrk để test performance

## 🎯 **Expected Results**

✅ **THÀNH CÔNG khi thấy:**
- Token được generate thành công  
- Signature validation pass
- Permission check pass
- API response `"isSuccess": true`
- Logs không có ERROR hoặc WARNING

❌ **CẦN KIỂM TRA khi thấy:**
- Response `"isSuccess": false`
- HTTP status 401, 403, 500
- Logs có ERROR hoặc WARNING
- Timeout hoặc connection refused

---

**🎉 Chúc bạn testing thành công! Nếu có vấn đề gì, hãy check logs và troubleshooting section.**