using System;

class Program
{
    static void Main()
    {
        Console.WriteLine("Testing password hashes...");
        
        // Test admin123
        var admin123Hash = BCrypt.Net.BCrypt.HashPassword("admin123");
        Console.WriteLine($"admin123 hash: {admin123Hash}");
        Console.WriteLine($"admin123 verify: {BCrypt.Net.BCrypt.Verify("admin123", admin123Hash)}");
        
        // Test user123
        var user123Hash = BCrypt.Net.BCrypt.HashPassword("user123");
        Console.WriteLine($"user123 hash: {user123Hash}");
        Console.WriteLine($"user123 verify: {BCrypt.Net.BCrypt.Verify("user123", user123Hash)}");
        
        // Test the hash from seed data
        var seedHash = "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi";
        Console.WriteLine($"Seed hash verify with 'password': {BCrypt.Net.BCrypt.Verify("password", seedHash)}");
        Console.WriteLine($"Seed hash verify with 'admin123': {BCrypt.Net.BCrypt.Verify("admin123", seedHash)}");
        Console.WriteLine($"Seed hash verify with 'user123': {BCrypt.Net.BCrypt.Verify("user123", seedHash)}");
    }
}