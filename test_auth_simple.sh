#!/bin/bash

# =================================================================
# TEST LUỒNG AUTHENTICATION/AUTHORIZATION ĐƠN GIẢN
# =================================================================

API_URL="https://localhost:5001"
echo "🔐 Test Authentication/Authorization Flow"
echo "📍 API URL: $API_URL"
echo

# =================================================================
# BƯỚC 1: ZenShop Authentication (Lấy token)
# =================================================================
echo "=== BƯỚC 1: ZenShop Authentication ==="
echo "1.1. ZenShop yêu cầu token..."

ZENSHOP_AUTH_RESPONSE=$(curl -s -X POST "$API_URL/api/authentication/token" \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "zenshop_client",
    "clientSecret": "password",
    "grantType": "client_credentials"
  }' \
  -k)

echo "📋 Response:"
echo "$ZENSHOP_AUTH_RESPONSE" | jq '.'

# Lấy token
ZENSHOP_TOKEN=$(echo "$ZENSHOP_AUTH_RESPONSE" | jq -r '.data.accessToken // empty')
AUTH_SUCCESS=$(echo "$ZENSHOP_AUTH_RESPONSE" | jq -r '.isSuccess // false')

if [ "$AUTH_SUCCESS" = "true" ] && [ -n "$ZENSHOP_TOKEN" ]; then
    echo "✅ Authentication thành công!"
    echo "🔑 Token: ${ZENSHOP_TOKEN:0:50}..."
    echo "📋 Scopes: $(echo "$ZENSHOP_AUTH_RESPONSE" | jq -r '.data.scope // "N/A"')"
else
    echo "❌ Authentication thất bại!"
    echo "🛑 Dừng test"
    exit 1
fi
echo

# =================================================================
# BƯỚC 2: Test Authorization (Gọi API với token)
# =================================================================
echo "=== BƯỚC 2: Test Authorization ==="
echo "2.1. ZenShop gọi MobiFone login API..."

# Tạo timestamp và signature
TIMESTAMP=$(date +%s)
SIGNATURE=$(echo -n "zenshop_client$TIMESTAMP" | sha256sum | cut -d' ' -f1)

MOBIFONE_LOGIN_RESPONSE=$(curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $ZENSHOP_TOKEN" \
  -H "X-Client-Id: zenshop_client" \
  -H "X-Timestamp: $TIMESTAMP" \
  -H "X-Signature: $SIGNATURE" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "test_user",
    "password": "test_password"
  }' \
  -k)

echo "📋 Response:"
echo "$MOBIFONE_LOGIN_RESPONSE" | jq '.'

# Kiểm tra response
MOBIFONE_CODE=$(echo "$MOBIFONE_LOGIN_RESPONSE" | jq -r '.Code // .code // "unknown"')
if [ "$MOBIFONE_CODE" = "401" ]; then
    echo "❌ Authorization thất bại - 401 Unauthorized"
    echo "🔍 Có thể là:"
    echo "  - Token không hợp lệ"
    echo "  - Middleware authentication chưa hoạt động"
    echo "  - Permission không đủ"
elif [ "$MOBIFONE_CODE" = "403" ]; then
    echo "❌ Authorization thất bại - 403 Forbidden"
    echo "🔍 ZenShop không có permission MOBIFONE_API_EXECUTE"
elif [ "$MOBIFONE_CODE" = "500" ]; then
    echo "⚠️ Server error - Logic API chưa implement"
    echo "✅ Nhưng Authorization đã PASS (vượt qua middleware)"
else
    echo "✅ Authorization thành công!"
    echo "🎯 API response: $MOBIFONE_CODE"
fi
echo

# =================================================================
# BƯỚC 3: Test Permission Denied (Gọi API không có quyền)
# =================================================================
echo "=== BƯỚC 3: Test Permission Denied ==="
echo "3.1. ZenShop gọi admin API (không có quyền)..."

ADMIN_RESPONSE=$(curl -s -X GET "$API_URL/api/client-credential" \
  -H "Authorization: Bearer $ZENSHOP_TOKEN" \
  -H "X-Client-Id: zenshop_client" \
  -H "X-Timestamp: $TIMESTAMP" \
  -H "X-Signature: $SIGNATURE" \
  -k)

echo "📋 Response:"
echo "$ADMIN_RESPONSE" | jq '.'

ADMIN_CODE=$(echo "$ADMIN_RESPONSE" | jq -r '.Code // .code // "unknown"')
if [ "$ADMIN_CODE" = "403" ]; then
    echo "✅ Permission control hoạt động đúng - 403 Forbidden"
elif [ "$ADMIN_CODE" = "401" ]; then
    echo "❌ Authentication middleware chưa hoạt động"
else
    echo "⚠️ Unexpected response: $ADMIN_CODE"
fi
echo

# =================================================================
# BƯỚC 4: Test Invalid Token
# =================================================================
echo "=== BƯỚC 4: Test Invalid Token ==="
echo "4.1. Gọi API với token giả..."

INVALID_RESPONSE=$(curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "Authorization: Bearer invalid_fake_token_123" \
  -H "X-Client-Id: zenshop_client" \
  -H "X-Timestamp: $TIMESTAMP" \
  -H "X-Signature: $SIGNATURE" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "test",
    "password": "test"
  }' \
  -k)

echo "📋 Response:"
echo "$INVALID_RESPONSE" | jq '.'

INVALID_CODE=$(echo "$INVALID_RESPONSE" | jq -r '.Code // .code // "unknown"')
if [ "$INVALID_CODE" = "401" ]; then
    echo "✅ Token validation hoạt động đúng - 401 Unauthorized"
else
    echo "❌ Token validation không hoạt động: $INVALID_CODE"
fi
echo

# =================================================================
# KẾT QUẢ TỔNG HỢP
# =================================================================
echo "=== 📊 KẾT QUẢ TỔNG HỢP ==="
echo "🔐 Authentication Flow:"
echo "  ├─ ZenShop login: $([ "$AUTH_SUCCESS" = "true" ] && echo "✅ SUCCESS" || echo "❌ FAILED")"
echo "  ├─ JWT token: $([ -n "$ZENSHOP_TOKEN" ] && echo "✅ ISSUED" || echo "❌ NOT ISSUED")"
echo "  ├─ API authorization: $([ "$MOBIFONE_CODE" != "401" ] && echo "✅ PASS" || echo "❌ FAIL")"
echo "  ├─ Permission control: $([ "$ADMIN_CODE" = "403" ] && echo "✅ WORKING" || echo "❌ NOT WORKING")"
echo "  └─ Token validation: $([ "$INVALID_CODE" = "401" ] && echo "✅ WORKING" || echo "❌ NOT WORKING")"
echo

echo "🎯 Cách hiểu kết quả:"
echo "  📍 401 = Authentication failed (token sai/thiếu)"
echo "  📍 403 = Authorization failed (không có permission)"
echo "  📍 500 = Server error (logic API chưa implement)"
echo "  📍 200 = Success (API hoạt động đúng)"
echo

echo "🚀 Luồng Authentication/Authorization:"
echo "  1️⃣ Client gọi /api/authentication/token để lấy JWT"
echo "  2️⃣ Client gọi API với Authorization: Bearer <token>"
echo "  3️⃣ Middleware kiểm tra token + permission"
echo "  4️⃣ API xử lý business logic"
echo

echo "🔧 Nếu có lỗi, check:"
echo "  - Middleware pipeline trong Program.cs"
echo "  - Database seed data (partners, permissions)"
echo "  - JWT configuration"
echo "  - Permission attributes trên controller"