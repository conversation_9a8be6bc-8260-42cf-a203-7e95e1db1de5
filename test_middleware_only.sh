#!/bin/bash

# =================================================================
# TEST CHỈ MIDDLEWARE PIPELINE (KHÔNG QUAN TÂM BUSINESS LOGIC)
# =================================================================

API_URL="https://localhost:5001"
CLIENT_ID="zenshop_client"
CLIENT_SECRET="password"

echo "🔧 TESTING MIDDLEWARE PIPELINE ONLY"
echo "📍 API URL: $API_URL"
echo

# Function to create HMAC signature
create_signature() {
    local http_method="$1"
    local request_path="$2"
    local timestamp="$3"
    local client_id="$4"
    local payload="$5"
    local hmac_secret="password"
    
    local string_to_sign="${http_method}\n${request_path}\n${timestamp}\n${client_id}\n${payload}"
    echo -n "$string_to_sign" | openssl dgst -sha256 -hmac "$hmac_secret" -binary | base64
}

# =================================================================
# STEP 1: Get valid token
# =================================================================
echo "=== STEP 1: Get Authentication Token ==="

AUTH_TIMESTAMP=$(date +%s)
AUTH_PAYLOAD='{"clientId":"'$CLIENT_ID'","clientSecret":"'$CLIENT_SECRET'","grantType":"client_credentials"}'
AUTH_SIGNATURE=$(create_signature "POST" "/api/authentication/token" "$AUTH_TIMESTAMP" "$CLIENT_ID" "$AUTH_PAYLOAD")

AUTH_RESPONSE=$(curl -s -X POST "$API_URL/api/authentication/token" \
  -H "Content-Type: application/json" \
  -H "X-Client-ID: $CLIENT_ID" \
  -H "X-Timestamp: $AUTH_TIMESTAMP" \
  -H "X-Signature: $AUTH_SIGNATURE" \
  -d "$AUTH_PAYLOAD" \
  -k)

echo "📋 Auth Response:"
echo "$AUTH_RESPONSE" | jq '.'

ACCESS_TOKEN=$(echo "$AUTH_RESPONSE" | jq -r '.data.accessToken // empty')
if [ -z "$ACCESS_TOKEN" ]; then
    echo "❌ Cannot get access token"
    exit 1
fi

echo "✅ Token obtained: ${ACCESS_TOKEN:0:50}..."
echo

# =================================================================
# STEP 2: Test middleware pipeline responses
# =================================================================
echo "=== STEP 2: Middleware Pipeline Tests ==="

API_TIMESTAMP=$(date +%s)
API_PAYLOAD='{"username":"test","password":"test"}'
API_SIGNATURE=$(create_signature "POST" "/api/mobifone-invoice/login" "$API_TIMESTAMP" "$CLIENT_ID" "$API_PAYLOAD")

echo "🔍 2.1. Valid request (should reach business logic)..."
VALID_RESPONSE=$(curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "X-Client-ID: $CLIENT_ID" \
  -H "X-Timestamp: $API_TIMESTAMP" \
  -H "X-Signature: $API_SIGNATURE" \
  -H "Content-Type: application/json" \
  -d "$API_PAYLOAD" \
  -k)

echo "📋 Valid Request Response:"
echo "$VALID_RESPONSE" | jq '.'

VALID_CODE=$(echo "$VALID_RESPONSE" | jq -r '.Code // .code // "unknown"')
echo "📊 Response Code: $VALID_CODE"

# Check if this is middleware rejection vs business logic
if [[ "$VALID_CODE" == "401" ]]; then
    if [[ "$VALID_RESPONSE" == *"Not implemented yet"* ]]; then
        echo "✅ MIDDLEWARE PASS - Reached business logic (MobiFone service returned error)"
    else
        echo "❌ MIDDLEWARE REJECT - Authentication/authorization failed"
    fi
elif [[ "$VALID_CODE" == "403" ]]; then
    echo "❌ MIDDLEWARE REJECT - Permission denied"
elif [[ "$VALID_CODE" == "400" ]]; then
    echo "❌ MIDDLEWARE REJECT - Bad request (missing headers/invalid signature)"
else
    echo "✅ MIDDLEWARE PASS - Request processed by business logic"
fi
echo

echo "🔍 2.2. Missing Authorization header..."
NO_AUTH_RESPONSE=$(curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "X-Client-ID: $CLIENT_ID" \
  -H "X-Timestamp: $API_TIMESTAMP" \
  -H "X-Signature: $API_SIGNATURE" \
  -H "Content-Type: application/json" \
  -d "$API_PAYLOAD" \
  -k)

NO_AUTH_CODE=$(echo "$NO_AUTH_RESPONSE" | jq -r '.Code // .code // "unknown"')
echo "📋 No Auth Response Code: $NO_AUTH_CODE"
echo "Expected: 401 (authentication middleware)"

echo "🔍 2.3. Invalid signature..."
INVALID_SIG_RESPONSE=$(curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "X-Client-ID: $CLIENT_ID" \
  -H "X-Timestamp: $API_TIMESTAMP" \
  -H "X-Signature: invalid_signature_123" \
  -H "Content-Type: application/json" \
  -d "$API_PAYLOAD" \
  -k)

INVALID_SIG_CODE=$(echo "$INVALID_SIG_RESPONSE" | jq -r '.Code // .code // "unknown"')
echo "📋 Invalid Signature Response Code: $INVALID_SIG_CODE" 
echo "Expected: 401 (signature middleware)"

echo "🔍 2.4. Missing X-Client-ID..."
NO_CLIENT_RESPONSE=$(curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "X-Timestamp: $API_TIMESTAMP" \
  -H "X-Signature: $API_SIGNATURE" \
  -H "Content-Type: application/json" \
  -d "$API_PAYLOAD" \
  -k)

NO_CLIENT_CODE=$(echo "$NO_CLIENT_RESPONSE" | jq -r '.Code // .code // "unknown"')
echo "📋 No Client-ID Response Code: $NO_CLIENT_CODE"
echo "Expected: 400 (missing header validation)"
echo

# =================================================================
# MIDDLEWARE ANALYSIS
# =================================================================
echo "=== 🎯 MIDDLEWARE PIPELINE ANALYSIS ==="
echo "🔧 Middleware Test Results:"
echo "  ├─ Valid Request: $VALID_CODE $([ "$VALID_CODE" != "401" ] && echo "(✅ PASS)" || ([ "$VALID_RESPONSE" == *"Not implemented yet"* ] && echo "(✅ PASS to business logic)" || echo "(❌ MIDDLEWARE BLOCK)"))"
echo "  ├─ No Authorization: $NO_AUTH_CODE $([ "$NO_AUTH_CODE" = "401" ] && echo "(✅ JWT middleware working)" || echo "(❌ JWT middleware not working)")"
echo "  ├─ Invalid Signature: $INVALID_SIG_CODE $([ "$INVALID_SIG_CODE" = "401" ] && echo "(✅ Signature middleware working)" || echo "(❌ Signature middleware not working)")"
echo "  └─ Missing Client-ID: $NO_CLIENT_CODE $([ "$NO_CLIENT_CODE" = "400" ] && echo "(✅ Header validation working)" || echo "(❌ Header validation not working)")"
echo

if [[ "$VALID_RESPONSE" == *"Not implemented yet"* ]]; then
    echo "🎉 SUCCESS: Full middleware pipeline is WORKING!"
    echo "💡 The 'Not implemented yet' response is from MobiFone business logic, not middleware"
    echo "🔒 All security layers (IP → JWT → Signature → Permission) are functioning correctly"
else
    echo "⚠️ Middleware pipeline needs attention"
    echo "🔍 Check middleware configuration and implementation"
fi

echo
echo "📝 CONCLUSION:"
echo "   If 'Not implemented yet' appears, middleware pipeline is working correctly."
echo "   The response indicates the request successfully passed all security checks"
echo "   and reached the business logic layer (MobiFone service)."