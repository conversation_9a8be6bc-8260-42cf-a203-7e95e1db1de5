namespace Shared.Responses;
public class Response<TEntity>
{
    public Response() { }

    public Response(TEntity data)
    {
        this.Data = data;
        this.Code = "000";
    }

    public Response(TEntity data, string message)
    {
        this.Data = data;
        this.Message = message;
        this.Code = "000";
    }

    public Response(TEntity data, string message, string code)
    {
        this.Data = data;
        this.Message = message;
        this.Code = code;
    }

    public string Code { get; set; } = "000";
    public string Message { get; set; } = string.Empty;
    public object? Errors { get; set; } = null;
    public string? TraceId { get; set; }
    public TEntity? Data { get; set; }

    /// <summary>
    /// Indicates if the operation was successful
    /// </summary>
    public bool IsSuccess => Code == "000";

    /// <summary>
    /// Create a successful response
    /// </summary>
    public static Response<T> Success<T>(T data, string? message = null)
    {
        return new Response<T>
        {
            Code = "000",
            Data = data,
            Message = message ?? string.Empty
        };
    }

    /// <summary>
    /// Create a failure response
    /// </summary>
    public static Response<T> Failure<T>(string message, string code = "500", string? errors = null)
    {
        return new Response<T>
        {
            Code = code,
            Message = message,
            Errors = errors,
            Data = default(T)
        };
    }
}