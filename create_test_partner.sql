-- Create test partner for authentication testing
-- Run this SQL on your PostgreSQL database

-- Insert test partner
INSERT INTO "Partners" (
    "Id",
    "ClientId", 
    "ClientSecretHash",
    "HmacSecretHash",
    "Name",
    "ContactEmail",
    "Description",
    "IpWhitelist",
    "EnableIpWhitelist",
    "IsActive",
    "ApiRateLimitPerHour",
    "MonthlyInvoiceLimit",
    "CurrentMonthUsage",
    "CreatedAt",
    "UpdatedAt",
    "IsDeleted"
) VALUES (
    gen_random_uuid(),
    'test-client',
    -- ClientSecret: "test-secret" hashed with BCrypt
    '$2a$11$Xa1SQeLQxwJ3tJPOm5EZceEP0FZRVg8Jl.uh1uLqF7dNePyD2YNjG',
    -- HmacSecret: "test-hmac-secret" hashed with BCrypt  
    '$2a$11$Xa1SQeLQxwJ3tJPOm5EZceEP0FZRVg8Jl.uh1uLqF7dNePyD2YNjG',
    'Test Partner',
    '<EMAIL>',
    'Test partner for API development',
    '["127.0.0.1", "::1", "0.0.0.0/0"]',
    false, -- Disable IP whitelist for testing
    true,
    10000,
    100000,
    0,
    NOW(),
    NOW(),
    false
);

-- Verify the partner was created
SELECT "Id", "ClientId", "Name", "IsActive" FROM "Partners" WHERE "ClientId" = 'test-client';