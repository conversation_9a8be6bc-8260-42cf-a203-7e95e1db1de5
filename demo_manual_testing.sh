#!/bin/bash

# =================================================================
# 🎯 MANUAL TESTING GUIDE - STEP BY STEP
# =================================================================

API_URL="https://localhost:5001"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
WHITE='\033[1;37m'
NC='\033[0m'

print_header() {
    echo -e "${WHITE}$1${NC}"
}

print_step() {
    echo -e "${BLUE}$1${NC}"
}

print_command() {
    echo -e "${YELLOW}$1${NC}"
}

clear
print_header "🎯 MANUAL TESTING GUIDE - Authentication/Authorization"
print_header "════════════════════════════════════════════════════════"
echo

echo "This guide will walk you through manually testing the authentication/authorization flow."
echo "You can copy and paste each command to test step by step."
echo

# =================================================================
# STEP 1: Authentication
# =================================================================
print_header "📋 STEP 1: Get Authentication Token"
echo "──────────────────────────────────────────────"
echo

print_step "1.1. Generate timestamp and signature:"
echo
print_command "# Get current timestamp"
print_command "TIMESTAMP=\$(date +%s)"
print_command "echo \"Timestamp: \$TIMESTAMP\""
echo

print_command "# Create HMAC signature"
print_command "CLIENT_ID=\"zenshop_client\""
print_command "PAYLOAD='{\"clientId\":\"zenshop_client\",\"clientSecret\":\"password\",\"grantType\":\"client_credentials\"}'"
print_command "STRING_TO_SIGN=\"POST\\n/api/authentication/token\\n\$TIMESTAMP\\n\$CLIENT_ID\\n\$PAYLOAD\""
print_command "SIGNATURE=\$(echo -n \"\$STRING_TO_SIGN\" | openssl dgst -sha256 -hmac \"password\" -binary | base64)"
print_command "echo \"Signature: \$SIGNATURE\""
echo

print_step "1.2. Call authentication API:"
echo
print_command "curl -X POST \"$API_URL/api/authentication/token\" \\"
print_command "  -H \"Content-Type: application/json\" \\"
print_command "  -H \"X-Client-ID: \$CLIENT_ID\" \\"
print_command "  -H \"X-Timestamp: \$TIMESTAMP\" \\"
print_command "  -H \"X-Signature: \$SIGNATURE\" \\"
print_command "  -d \"\$PAYLOAD\" \\"
print_command "  -k | jq '.'"
echo

print_step "1.3. Extract access token:"
echo
print_command "# Save the response and extract token"
print_command "TOKEN_RESPONSE=\$(curl -s -X POST \"$API_URL/api/authentication/token\" \\..."
print_command "ACCESS_TOKEN=\$(echo \"\$TOKEN_RESPONSE\" | jq -r '.data.accessToken')"
print_command "echo \"Access Token: \${ACCESS_TOKEN:0:50}...\""
echo

# =================================================================
# STEP 2: Protected API Access
# =================================================================
print_header "📋 STEP 2: Access Protected API"
echo "──────────────────────────────────────────────"
echo

print_step "2.1. Prepare API request:"
echo
print_command "# Generate new timestamp and signature for API call"
print_command "API_TIMESTAMP=\$(date +%s)"
print_command "API_PAYLOAD='{\"username\":\"demo_user\",\"password\":\"demo_pass\"}'"
print_command "API_STRING_TO_SIGN=\"POST\\n/api/mobifone-invoice/login\\n\$API_TIMESTAMP\\n\$CLIENT_ID\\n\$API_PAYLOAD\""
print_command "API_SIGNATURE=\$(echo -n \"\$API_STRING_TO_SIGN\" | openssl dgst -sha256 -hmac \"password\" -binary | base64)"
echo

print_step "2.2. Call protected API:"
echo
print_command "curl -X POST \"$API_URL/api/mobifone-invoice/login\" \\"
print_command "  -H \"Authorization: Bearer \$ACCESS_TOKEN\" \\"
print_command "  -H \"X-Client-ID: \$CLIENT_ID\" \\"
print_command "  -H \"X-Timestamp: \$API_TIMESTAMP\" \\"
print_command "  -H \"X-Signature: \$API_SIGNATURE\" \\"
print_command "  -H \"Content-Type: application/json\" \\"
print_command "  -d \"\$API_PAYLOAD\" \\"
print_command "  -k | jq '.'"
echo

# =================================================================
# STEP 3: Security Testing
# =================================================================
print_header "📋 STEP 3: Security Validation Tests"
echo "──────────────────────────────────────────────"
echo

print_step "3.1. Test missing Authorization header:"
echo
print_command "curl -X POST \"$API_URL/api/mobifone-invoice/login\" \\"
print_command "  -H \"X-Client-ID: \$CLIENT_ID\" \\"
print_command "  -H \"X-Timestamp: \$API_TIMESTAMP\" \\"
print_command "  -H \"X-Signature: \$API_SIGNATURE\" \\"
print_command "  -H \"Content-Type: application/json\" \\"
print_command "  -d \"\$API_PAYLOAD\" \\"
print_command "  -k | jq '.'"
print_command "# Expected: 401 Unauthorized"
echo

print_step "3.2. Test invalid signature:"
echo
print_command "curl -X POST \"$API_URL/api/mobifone-invoice/login\" \\"
print_command "  -H \"Authorization: Bearer \$ACCESS_TOKEN\" \\"
print_command "  -H \"X-Client-ID: \$CLIENT_ID\" \\"
print_command "  -H \"X-Timestamp: \$API_TIMESTAMP\" \\"
print_command "  -H \"X-Signature: invalid_signature_123\" \\"
print_command "  -H \"Content-Type: application/json\" \\"
print_command "  -d \"\$API_PAYLOAD\" \\"
print_command "  -k | jq '.'"
print_command "# Expected: 401 Unauthorized"
echo

print_step "3.3. Test expired timestamp:"
echo
print_command "OLD_TIMESTAMP=\$(($(date +%s) - 600))  # 10 minutes ago"
print_command "OLD_SIGNATURE=\$(echo -n \"POST\\n/api/mobifone-invoice/login\\n\$OLD_TIMESTAMP\\n\$CLIENT_ID\\n\$API_PAYLOAD\" | openssl dgst -sha256 -hmac \"password\" -binary | base64)"
print_command "curl -X POST \"$API_URL/api/mobifone-invoice/login\" \\"
print_command "  -H \"Authorization: Bearer \$ACCESS_TOKEN\" \\"
print_command "  -H \"X-Client-ID: \$CLIENT_ID\" \\"
print_command "  -H \"X-Timestamp: \$OLD_TIMESTAMP\" \\"
print_command "  -H \"X-Signature: \$OLD_SIGNATURE\" \\"
print_command "  -H \"Content-Type: application/json\" \\"
print_command "  -d \"\$API_PAYLOAD\" \\"
print_command "  -k | jq '.'"
print_command "# Expected: 401 Unauthorized (timestamp too old)"
echo

print_step "3.4. Test permission denied (admin endpoint):"
echo
print_command "ADMIN_TIMESTAMP=\$(date +%s)"
print_command "ADMIN_SIGNATURE=\$(echo -n \"GET\\n/api/client-credential\\n\$ADMIN_TIMESTAMP\\n\$CLIENT_ID\\n\" | openssl dgst -sha256 -hmac \"password\" -binary | base64)"
print_command "curl -X GET \"$API_URL/api/client-credential\" \\"
print_command "  -H \"Authorization: Bearer \$ACCESS_TOKEN\" \\"
print_command "  -H \"X-Client-ID: \$CLIENT_ID\" \\"
print_command "  -H \"X-Timestamp: \$ADMIN_TIMESTAMP\" \\"
print_command "  -H \"X-Signature: \$ADMIN_SIGNATURE\" \\"
print_command "  -k | jq '.'"
print_command "# Expected: 403 Forbidden (no admin permission)"
echo

# =================================================================
# STEP 4: Complete Test Script
# =================================================================
print_header "📋 STEP 4: Complete Test Script"
echo "──────────────────────────────────────────────"
echo

print_step "4.1. Copy this complete script for easy testing:"
echo
print_command "#!/bin/bash"
print_command ""
print_command "# Authentication"
print_command "TIMESTAMP=\$(date +%s)"
print_command "CLIENT_ID=\"zenshop_client\""
print_command "PAYLOAD='{\"clientId\":\"zenshop_client\",\"clientSecret\":\"password\",\"grantType\":\"client_credentials\"}'"
print_command "STRING_TO_SIGN=\"POST\\n/api/authentication/token\\n\$TIMESTAMP\\n\$CLIENT_ID\\n\$PAYLOAD\""
print_command "SIGNATURE=\$(echo -n \"\$STRING_TO_SIGN\" | openssl dgst -sha256 -hmac \"password\" -binary | base64)"
print_command ""
print_command "echo \"Getting access token...\""
print_command "TOKEN_RESPONSE=\$(curl -s -X POST \"$API_URL/api/authentication/token\" \\"
print_command "  -H \"Content-Type: application/json\" \\"
print_command "  -H \"X-Client-ID: \$CLIENT_ID\" \\"
print_command "  -H \"X-Timestamp: \$TIMESTAMP\" \\"
print_command "  -H \"X-Signature: \$SIGNATURE\" \\"
print_command "  -d \"\$PAYLOAD\" \\"
print_command "  -k)"
print_command ""
print_command "ACCESS_TOKEN=\$(echo \"\$TOKEN_RESPONSE\" | jq -r '.data.accessToken')"
print_command "echo \"Token: \${ACCESS_TOKEN:0:50}...\""
print_command ""
print_command "# API Call"
print_command "API_TIMESTAMP=\$(date +%s)"
print_command "API_PAYLOAD='{\"username\":\"demo\",\"password\":\"demo\"}'"
print_command "API_SIGNATURE=\$(echo -n \"POST\\n/api/mobifone-invoice/login\\n\$API_TIMESTAMP\\n\$CLIENT_ID\\n\$API_PAYLOAD\" | openssl dgst -sha256 -hmac \"password\" -binary | base64)"
print_command ""
print_command "echo \"Calling protected API...\""
print_command "curl -X POST \"$API_URL/api/mobifone-invoice/login\" \\"
print_command "  -H \"Authorization: Bearer \$ACCESS_TOKEN\" \\"
print_command "  -H \"X-Client-ID: \$CLIENT_ID\" \\"
print_command "  -H \"X-Timestamp: \$API_TIMESTAMP\" \\"
print_command "  -H \"X-Signature: \$API_SIGNATURE\" \\"
print_command "  -H \"Content-Type: application/json\" \\"
print_command "  -d \"\$API_PAYLOAD\" \\"
print_command "  -k | jq '.'"
echo

# =================================================================
# EXPECTED RESULTS
# =================================================================
print_header "📋 EXPECTED RESULTS"
echo "──────────────────────────────────────────────"
echo

print_step "✅ Successful Authentication Response:"
echo '{
  "code": "000",
  "message": "Token issued successfully",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 7200,
    "scope": "invoice_create_create invoice_create_execute mobifone_api_execute ..."
  },
  "isSuccess": true
}'
echo

print_step "✅ Successful API Access Response:"
echo '{
  "Code": "401",
  "Message": "Not implemented yet",
  "TraceId": "...",
  "Data": null,
  "IsSuccess": false
}'
print_step "Note: 'Not implemented yet' means security passed, reached business logic!"
echo

print_step "❌ Failed Authentication/Authorization:"
echo '{
  "Code": "401",
  "Message": "Unauthorized",
  "Data": null,
  "IsSuccess": false
}'
echo

print_header "🎯 TESTING TIPS"
echo "──────────────────────────────────────────────"
echo "• Use 'jq' for better JSON formatting"
echo "• Check response codes: 200=success, 401=auth fail, 403=permission denied"
echo "• 'Not implemented yet' = Security passed, business logic placeholder"
echo "• Save tokens in variables for reuse"
echo "• Test negative scenarios to verify security"
echo

print_header "🚀 Ready to test! Copy and paste the commands above."