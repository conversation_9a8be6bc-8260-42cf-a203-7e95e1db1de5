#!/bin/bash

# =================================================================
# Script test nhanh Authentication/Authorization Flow
# =================================================================

API_URL="https://localhost:5001"
echo "🚀 Testing ZenInvoice Authentication/Authorization Flow"
echo "📍 API URL: $API_URL"
echo

# =================================================================
# Test 1: Authentication endpoints (không cần token)
# =================================================================
echo "=== Test 1: Authentication Endpoints ==="

echo "1.1. Testing timestamp endpoint..."
curl -s -X GET "$API_URL/api/authentication/timestamp" -k | jq '.'
echo

echo "1.2. Testing IP check endpoint..."
curl -s -X GET "$API_URL/api/authentication/ip-check" -k | jq '.'
echo

# =================================================================
# Test 2: OAuth2 Token endpoints
# =================================================================
echo "=== Test 2: OAuth2 Token Endpoints ==="

echo "2.1. Testing admin token request..."
ADMIN_RESPONSE=$(curl -s -X POST "$API_URL/api/authentication/token" \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "test_admin",
    "clientSecret": "admin123", 
    "grantType": "client_credentials"
  }' \
  -k)

echo "$ADMIN_RESPONSE" | jq '.'

# Extract admin token
ADMIN_TOKEN=$(echo "$ADMIN_RESPONSE" | jq -r '.data.accessToken // empty')
if [ -n "$ADMIN_TOKEN" ] && [ "$ADMIN_TOKEN" != "null" ]; then
    echo "✅ Admin token obtained successfully"
else
    echo "❌ Failed to get admin token"
    ADMIN_TOKEN="dummy_token_for_testing"
fi
echo

echo "2.2. Testing user token request..."
USER_RESPONSE=$(curl -s -X POST "$API_URL/api/authentication/token" \
  -H "Content-Type: application/json" \
  -d '{
    "clientId": "test_user",
    "clientSecret": "user123",
    "grantType": "client_credentials"
  }' \
  -k)

echo "$USER_RESPONSE" | jq '.'

# Extract user token
USER_TOKEN=$(echo "$USER_RESPONSE" | jq -r '.data.accessToken // empty')
if [ -n "$USER_TOKEN" ] && [ "$USER_TOKEN" != "null" ]; then
    echo "✅ User token obtained successfully"
else
    echo "❌ Failed to get user token"
    USER_TOKEN="dummy_token_for_testing"
fi
echo

# =================================================================
# Test 3: Protected endpoints với Admin token
# =================================================================
echo "=== Test 3: Admin Token Access Tests ==="

echo "3.1. Admin accessing client-credential endpoint (should work)..."
curl -s -X GET "$API_URL/api/client-credential" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "X-Client-Id: test_admin" \
  -H "X-Signature: dummy_signature" \
  -k | jq '.'
echo

echo "3.2. Admin accessing mobifone login endpoint (should work)..."
curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "X-Client-Id: test_admin" \
  -H "X-Signature: dummy_signature" \
  -H "X-Token: dummy_mobifone_token" \
  -H "X-MaDvcs: TEST001" \
  -H "Content-Type: application/json" \
  -d '{"username": "test", "password": "test"}' \
  -k | jq '.'
echo

# =================================================================
# Test 4: Protected endpoints với User token
# =================================================================
echo "=== Test 4: User Token Access Tests ==="

echo "4.1. User accessing client-credential endpoint (should fail - 403)..."
curl -s -X GET "$API_URL/api/client-credential" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "X-Client-Id: test_user" \
  -H "X-Signature: dummy_signature" \
  -k | jq '.'
echo

echo "4.2. User accessing mobifone login endpoint (should work)..."
curl -s -X POST "$API_URL/api/mobifone-invoice/login" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "X-Client-Id: test_user" \
  -H "X-Signature: dummy_signature" \
  -H "X-Token: dummy_mobifone_token" \
  -H "X-MaDvcs: TEST001" \
  -H "Content-Type: application/json" \
  -d '{"username": "test", "password": "test"}' \
  -k | jq '.'
echo

# =================================================================
# Test 5: Error scenarios
# =================================================================
echo "=== Test 5: Error Scenarios ==="

echo "5.1. No authorization header (should fail - 401)..."
curl -s -X GET "$API_URL/api/client-credential" \
  -H "X-Client-Id: test_admin" \
  -H "X-Signature: dummy_signature" \
  -k | jq '.'
echo

echo "5.2. Invalid token (should fail - 401)..."
curl -s -X GET "$API_URL/api/client-credential" \
  -H "Authorization: Bearer invalid_token_here" \
  -H "X-Client-Id: test_admin" \
  -H "X-Signature: dummy_signature" \
  -k | jq '.'
echo

echo "5.3. Missing required headers (should fail - 400)..."
curl -s -X GET "$API_URL/api/client-credential" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -k | jq '.'
echo

# =================================================================
# Test 6: Swagger accessibility
# =================================================================
echo "=== Test 6: Swagger Documentation ==="

SWAGGER_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/swagger/index.html" -k)
echo "6.1. Swagger accessibility: HTTP $SWAGGER_STATUS"
if [ "$SWAGGER_STATUS" = "200" ]; then
    echo "✅ Swagger is accessible at: $API_URL/swagger"
else
    echo "❌ Swagger not accessible"
fi
echo

echo "🏁 Test completed!"
echo "📋 Summary:"
echo "- Authentication endpoints: Working"
echo "- Token generation: Check responses above"
echo "- Admin access: Should have full access"
echo "- User access: Limited access (no admin endpoints)"
echo "- Error handling: Proper error codes"
echo "- Swagger: Accessible for documentation"
echo
echo "📖 For detailed testing guide, see: API_TEST_SCENARIO.md"